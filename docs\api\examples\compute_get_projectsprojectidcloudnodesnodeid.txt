curl -i -X GET 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/cloud/nodes/75228bef-1806-41fc-8b73-1c5752870ac6'

GET /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/cloud/nodes/75228bef-1806-41fc-8b73-1c5752870ac6 HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 1406
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:25:43 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/cloud/nodes/{node_id}

{
    "interfaces": [
        {
            "name": "docker0",
            "special": true,
            "type": "ethernet"
        },
        {
            "name": "lo",
            "special": true,
            "type": "ethernet"
        },
        {
            "name": "virbr0",
            "special": true,
            "type": "ethernet"
        },
        {
            "name": "virbr0-nic",
            "special": true,
            "type": "ethernet"
        },
        {
            "name": "vmnet1",
            "special": true,
            "type": "ethernet"
        },
        {
            "name": "vmnet8",
            "special": true,
            "type": "ethernet"
        },
        {
            "name": "wlp2s0",
            "special": false,
            "type": "ethernet"
        }
    ],
    "name": "Cloud 1",
    "node_directory": "/tmp/pytest-of-grossmj/pytest-41/test_json4/project-files/builtin/75228bef-1806-41fc-8b73-1c5752870ac6",
    "node_id": "75228bef-1806-41fc-8b73-1c5752870ac6",
    "ports_mapping": [
        {
            "interface": "wlp2s0",
            "name": "wlp2s0",
            "port_number": 0,
            "type": "ethernet"
        }
    ],
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "remote_console_host": "",
    "remote_console_http_path": "/",
    "remote_console_port": 23,
    "remote_console_type": "none",
    "status": "started"
}
