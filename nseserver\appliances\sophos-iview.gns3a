{"appliance_id": "270475da-094f-4479-8dd4-f2b9cb7a3949", "name": "Sophos iView", "category": "guest", "description": "Monitoring a distributed network across multiple locations can be a challenge. That's where Sophos iView can help. It provides you with an intelligent, uninterrupted view of your network from a single pane of glass. If you have multiple appliances, need consolidated reporting, or could just use help with log management or compliance, Sophos iView is the ideal solution.", "vendor_name": "<PERSON>ph<PERSON>", "vendor_url": "https://www.sophos.com", "documentation_url": "https://www.sophos.com/en-us/support/documentation/sophos-iview.aspx", "product_name": "Sophos iView", "product_url": "https://www.sophos.com/en-us/products/next-gen-firewall.aspx", "registry_version": 4, "status": "experimental", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "Default CLI password: admin\nDe<PERSON>ult WebUI address: http://************\nDefault WebUI credentials: admin / admin", "symbol": "mgmt_station.svg", "qemu": {"adapter_type": "virtio-net-pci", "adapters": 4, "ram": 4096, "hda_disk_interface": "virtio", "hdb_disk_interface": "virtio", "arch": "x86_64", "console_type": "telnet", "kvm": "require"}, "images": [{"filename": "VI-SIVOS_03.01.2.KVM-009-PRIMARY.qcow2", "version": "3.1.2", "md5sum": "62551f70f71e08283d3a23929321eba9", "filesize": 644218880, "download_url": "https://secure2.sophos.com/en-us/products/next-gen-firewall/free-trial.aspx"}, {"filename": "VI-SIVOS_02.00.0_MR-2.KVM-776-PRIMARY.qcow2", "version": "2.0.0 MR2", "md5sum": "d78c6f0c42186a4c606d7e57f2f3a6d7", "filesize": 493289472, "download_url": "https://secure2.sophos.com/en-us/products/next-gen-firewall/free-trial.aspx"}, {"filename": "VI-SIVOS-AUXILARY.qcow2", "version": "2.0.0 MR2", "md5sum": "a52d8cedb1ccd4b5b9f2723dfb41588b", "filesize": 204800, "download_url": "https://secure2.sophos.com/en-us/products/next-gen-firewall/free-trial.aspx"}], "versions": [{"name": "3.1.2", "images": {"hda_disk_image": "VI-SIVOS_03.01.2.KVM-009-PRIMARY.qcow2", "hdb_disk_image": "VI-SIVOS-AUXILARY.qcow2"}}, {"name": "2.0.0 MR2", "images": {"hda_disk_image": "VI-SIVOS_02.00.0_MR-2.KVM-776-PRIMARY.qcow2", "hdb_disk_image": "VI-SIVOS-AUXILARY.qcow2"}}]}