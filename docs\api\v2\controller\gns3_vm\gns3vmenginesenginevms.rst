/v2/gns3vm/engines/{engine}/vms
------------------------------------------------------------------------------------------------------------------------------------------

.. contents::

GET /v2/gns3vm/engines/**{engine}**/vms
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Get all the available VMs for a specific virtualization engine

Parameters
**********
- **engine**: Virtualization engine name

Response status codes
**********************
- **200**: Success
- **400**: Invalid request

Sample session
***************


.. literalinclude:: ../../../examples/controller_get_gns3vmenginesenginevms.txt

