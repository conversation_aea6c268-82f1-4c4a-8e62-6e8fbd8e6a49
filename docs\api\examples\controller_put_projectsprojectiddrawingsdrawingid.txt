curl -i -X PUT 'http://localhost:3080/v2/projects/6d93155f-b360-4b6e-a036-277cf4f076e5/drawings/fa60bbc1-49d7-4122-92a3-0dff9063058e' -d '{"x": 42}'

PUT /v2/projects/6d93155f-b360-4b6e-a036-277cf4f076e5/drawings/fa60bbc1-49d7-4122-92a3-0dff9063058e HTTP/1.1
{
    "x": 42
}


HTTP/1.1 201
Connection: close
Content-Length: 344
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:57 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/drawings/{drawing_id}

{
    "drawing_id": "fa60bbc1-49d7-4122-92a3-0dff9063058e",
    "locked": false,
    "project_id": "6d93155f-b360-4b6e-a036-277cf4f076e5",
    "rotation": 0,
    "svg": "<svg height=\"210\" width=\"500\"><line x1=\"0\" y1=\"0\" x2=\"200\" y2=\"200\" style=\"stroke:rgb(255,0,0);stroke-width:2\" /></svg>",
    "x": 42,
    "y": 20,
    "z": 0
}
