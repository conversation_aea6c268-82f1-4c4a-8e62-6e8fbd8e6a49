curl -i -X PUT 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/virtualbox/nodes/3fcaa4d4-e3ea-492e-967d-38cdc38d8100' -d '{"console": 5005, "name": "test"}'

PUT /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/virtualbox/nodes/3fcaa4d4-e3ea-492e-967d-38cdc38d8100 HTTP/1.1
{
    "console": 5005,
    "name": "test"
}


HTTP/1.1 200
Connection: close
Content-Length: 481
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:28 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/virtualbox/nodes/{node_id}

{
    "adapter_type": "Intel PRO/1000 MT Desktop (82540EM)",
    "adapters": 0,
    "console": 5005,
    "console_type": "telnet",
    "headless": false,
    "linked_clone": false,
    "name": "test",
    "node_directory": null,
    "node_id": "3fcaa4d4-e3ea-492e-967d-38cdc38d8100",
    "on_close": "power_off",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "ram": 0,
    "status": "stopped",
    "usage": "",
    "use_any_adapter": false,
    "vmname": "VMTEST"
}
