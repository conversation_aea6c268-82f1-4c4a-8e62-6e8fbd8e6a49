curl -i -X POST 'http://localhost:3080/v2/projects/25402c96-0aeb-4b65-9bce-4b36ed725b1b/snapshots' -d '{"name": "snap1"}'

POST /v2/projects/25402c96-0aeb-4b65-9bce-4b36ed725b1b/snapshots HTTP/1.1
{
    "name": "snap1"
}


HTTP/1.1 201
Connection: close
Content-Length: 170
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:29:18 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/snapshots

{
    "created_at": 1578450535,
    "name": "snap1",
    "project_id": "25402c96-0aeb-4b65-9bce-4b36ed725b1b",
    "snapshot_id": "d7f7b452-0f8b-4f9d-b67e-8119fcf93898"
}
