curl -i -X PUT 'http://localhost:3080/v2/projects/7b326169-f2e1-46ed-baf0-89bf4613d8ef/nodes/5a2cdb2f-584d-437c-b917-7605004ce6ae' -d '{"compute_id": "example.com", "name": "test", "node_type": "vpcs", "properties": {"startup_script": "echo test"}}'

PUT /v2/projects/7b326169-f2e1-46ed-baf0-89bf4613d8ef/nodes/5a2cdb2f-584d-437c-b917-7605004ce6ae HTTP/1.1
{
    "compute_id": "example.com",
    "name": "test",
    "node_type": "vpcs",
    "properties": {
        "startup_script": "echo test"
    }
}


HTTP/1.1 200
Connection: close
Content-Length: 1158
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:59 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/nodes/{node_id}

{
    "command_line": null,
    "compute_id": "example.com",
    "console": 2048,
    "console_auto_start": false,
    "console_host": "<MagicMock name='mock.console_host' id='140147702701360'>",
    "console_type": null,
    "custom_adapters": [],
    "first_port_name": null,
    "height": 59,
    "label": {
        "rotation": 0,
        "style": null,
        "text": "test",
        "x": null,
        "y": -40
    },
    "locked": false,
    "name": "test",
    "node_directory": null,
    "node_id": "5a2cdb2f-584d-437c-b917-7605004ce6ae",
    "node_type": "vpcs",
    "port_name_format": "Ethernet{0}",
    "port_segment_size": 0,
    "ports": [
        {
            "adapter_number": 0,
            "data_link_types": {
                "Ethernet": "DLT_EN10MB"
            },
            "link_type": "ethernet",
            "name": "Ethernet0",
            "port_number": 0,
            "short_name": "e0"
        }
    ],
    "project_id": "7b326169-f2e1-46ed-baf0-89bf4613d8ef",
    "properties": {},
    "status": "stopped",
    "symbol": ":/symbols/computer.svg",
    "template_id": null,
    "width": 65,
    "x": 0,
    "y": 0,
    "z": 1
}
