{"appliance_id": "f82b74c4-0f30-456f-a582-63daca528502", "name": "VyOS Universal Router", "category": "router", "description": "VyOS is an open-source network operating system that provides a comprehensive suite of features for routing, firewalling, and VPN functionality. VyOS offers a robust and flexible solution for both small-scale and large-scale network environments. It is designed to support enterprise-grade networking with the added benefits of community-driven development and continuous updates.\n\nThe VyOS Universal Router, when used in GNS3, brings the power and versatility of VyOS to network simulation and emulation. GNS3 users can deploy the VyOS Universal Router to create and test complex network topologies in a virtual environment. This appliance provides a rich set of features, including dynamic routing protocols, stateful firewall capabilities, various VPNs, as well as high availability configurations.\n\nThe seamless integration with GNS3 allows network engineers and architects to validate network designs, perform testing and troubleshooting, and enhance their skill sets in a controlled, risk-free environment.", "vendor_name": "VyOS Inc.", "vendor_url": "https://vyos.io/", "vendor_logo_url": "https://raw.githubusercontent.com/GNS3/gns3-registry/master/vendor-logos/VyOS.png", "documentation_url": "https://docs.vyos.io/", "product_name": "VyOS Universal Router", "product_url": "https://vyos.io/vyos-universal-router", "registry_version": 4, "status": "stable", "availability": "service-contract", "maintainer": "VyOS Inc.", "maintainer_email": "<EMAIL>", "usage": "\nDefault credentials:\nUser: vyos\nPassword: vyos", "symbol": "vyos.svg", "port_name_format": "eth{0}", "qemu": {"adapter_type": "virtio-net-pci", "adapters": 10, "ram": 2048, "cpus": 4, "hda_disk_interface": "virtio", "arch": "x86_64", "console_type": "telnet", "boot_priority": "c", "kvm": "require", "on_close": "shutdown_signal"}, "images": [{"filename": "vyos-1.4.1-kvm-amd64.qcow2", "version": "1.4.1", "md5sum": "5b8ebcd9905719d24405ed093afdd8ba", "filesize": 513736704, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.4.0-kvm-amd64.qcow2", "version": "1.4.0", "md5sum": "a130e446bc5bf87391981f183ee3694b", "filesize": 468320256, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.3.7-qemu-amd64.qcow2", "version": "1.3.7", "md5sum": "f4663b1e2df115bfa5c7ec17584514d6", "filesize": 359792640, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.3.2-10G-qemu.qcow2", "version": "1.3.2", "md5sum": "68ad3fb530213189ac9ed496d5fe7897", "filesize": 326893568, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.3.1-S1-10G-qemu.qcow2", "version": "1.3.1-S1", "md5sum": "d8ed9f82a983295b94b07f8e37c48ed0", "filesize": 343801856, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.3.1-10G-qemu.qcow2", "version": "1.3.1", "md5sum": "482367c833990fb2b9350e3708d33dc9", "filesize": 342556672, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.3.0-10G-qemu.qcow2", "version": "1.3.0", "md5sum": "086e95e992e9b4d014c5f154cd01a6e6", "filesize": 330956800, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.2.9-S1-10G-qemu.qcow2", "version": "1.2.9-S1", "md5sum": "0a70d78b80a3716d42487c02ef44f41f", "filesize": 426967040, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.2.8-10G-qemu.qcow2", "version": "1.2.8", "md5sum": "96c76f619d0f8ea11dc8a3a18ed67b98", "filesize": 425852928, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.2.7-qemu.qcow2", "version": "1.2.7", "md5sum": "1be4674c970fcdd65067e504baea5d74", "filesize": 424607744, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.2.6-qemu.qcow2", "version": "1.2.6", "md5sum": "d8010d79889ca0ba5cb2634665e548e3", "filesize": 424607744, "download_url": "https://support.vyos.io/"}, {"filename": "vyos-1.2.5-amd64.qcow2", "version": "1.2.5", "md5sum": "110c22309ec480600446fd2fb4f27a0d", "filesize": 411500544, "download_url": "https://support.vyos.io/"}], "versions": [{"name": "1.4.1", "images": {"hda_disk_image": "vyos-1.4.1-kvm-amd64.qcow2"}}, {"name": "1.4.0", "images": {"hda_disk_image": "vyos-1.4.0-kvm-amd64.qcow2"}}, {"name": "1.3.7", "images": {"hda_disk_image": "vyos-1.3.7-qemu-amd64.qcow2"}}, {"name": "1.3.2", "images": {"hda_disk_image": "vyos-1.3.2-10G-qemu.qcow2"}}, {"name": "1.3.1-S1", "images": {"hda_disk_image": "vyos-1.3.1-S1-10G-qemu.qcow2"}}, {"name": "1.3.1", "images": {"hda_disk_image": "vyos-1.3.1-10G-qemu.qcow2"}}, {"name": "1.3.0", "images": {"hda_disk_image": "vyos-1.3.0-10G-qemu.qcow2"}}, {"name": "1.2.9-S1", "images": {"hda_disk_image": "vyos-1.2.9-S1-10G-qemu.qcow2"}}, {"name": "1.2.8", "images": {"hda_disk_image": "vyos-1.2.8-10G-qemu.qcow2"}}, {"name": "1.2.7", "images": {"hda_disk_image": "vyos-1.2.7-qemu.qcow2"}}, {"name": "1.2.6", "images": {"hda_disk_image": "vyos-1.2.6-qemu.qcow2"}}, {"name": "1.2.5", "images": {"hda_disk_image": "vyos-1.2.5-amd64.qcow2"}}]}