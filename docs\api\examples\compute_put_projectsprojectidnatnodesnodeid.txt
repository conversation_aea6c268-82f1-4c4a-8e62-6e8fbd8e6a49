curl -i -X PUT 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/nat/nodes/da077ceb-e0a3-4143-a413-afc69064ac34' -d '{"name": "test"}'

PUT /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/nat/nodes/da077ceb-e0a3-4143-a413-afc69064ac34 HTTP/1.1
{
    "name": "test"
}


HTTP/1.1 200
Connection: close
Content-Length: 334
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:16 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/nat/nodes/{node_id}

{
    "name": "test",
    "node_id": "da077ceb-e0a3-4143-a413-afc69064ac34",
    "ports_mapping": [
        {
            "interface": "virbr0",
            "name": "nat0",
            "port_number": 0,
            "type": "ethernet"
        }
    ],
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "started"
}
