curl -i -X POST 'http://localhost:3080/v2/projects/44a8639a-c5e4-41f6-b4ad-f6577ba2f2a9/links' -d '{"nodes": [{"adapter_number": 0, "label": {"text": "Text", "x": 42, "y": 0}, "node_id": "6025514b-8798-44ca-8531-26129710b202", "port_number": 3}, {"adapter_number": 0, "node_id": "6025514b-8798-44ca-8531-26129710b202", "port_number": 4}]}'

POST /v2/projects/44a8639a-c5e4-41f6-b4ad-f6577ba2f2a9/links HTTP/1.1
{
    "nodes": [
        {
            "adapter_number": 0,
            "label": {
                "text": "Text",
                "x": 42,
                "y": 0
            },
            "node_id": "6025514b-8798-44ca-8531-26129710b202",
            "port_number": 3
        },
        {
            "adapter_number": 0,
            "node_id": "6025514b-8798-44ca-8531-26129710b202",
            "port_number": 4
        }
    ]
}


HTTP/1.1 409
Connection: close
Content-Length: 64
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/links

{
    "message": "Cannot connect to itself",
    "status": 409
}
