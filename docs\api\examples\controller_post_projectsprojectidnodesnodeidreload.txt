curl -i -X POST 'http://localhost:3080/v2/projects/7c0e4983-9140-4b78-ba09-f20084cf2e89/nodes/52895d91-5f7c-491f-98bc-12991e788546/reload' -d '{}'

POST /v2/projects/7c0e4983-9140-4b78-ba09-f20084cf2e89/nodes/52895d91-5f7c-491f-98bc-12991e788546/reload HTTP/1.1
{}


HTTP/1.1 200
Connection: close
Content-Length: 1158
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:00 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/nodes/{node_id}/reload

{
    "command_line": null,
    "compute_id": "example.com",
    "console": null,
    "console_auto_start": false,
    "console_host": "<MagicMock name='mock.console_host' id='140147693925152'>",
    "console_type": null,
    "custom_adapters": [],
    "first_port_name": null,
    "height": 59,
    "label": {
        "rotation": 0,
        "style": null,
        "text": "test",
        "x": null,
        "y": -40
    },
    "locked": false,
    "name": "test",
    "node_directory": null,
    "node_id": "52895d91-5f7c-491f-98bc-12991e788546",
    "node_type": "vpcs",
    "port_name_format": "Ethernet{0}",
    "port_segment_size": 0,
    "ports": [
        {
            "adapter_number": 0,
            "data_link_types": {
                "Ethernet": "DLT_EN10MB"
            },
            "link_type": "ethernet",
            "name": "Ethernet0",
            "port_number": 0,
            "short_name": "e0"
        }
    ],
    "project_id": "7c0e4983-9140-4b78-ba09-f20084cf2e89",
    "properties": {},
    "status": "stopped",
    "symbol": ":/symbols/computer.svg",
    "template_id": null,
    "width": 65,
    "x": 0,
    "y": 0,
    "z": 1
}
