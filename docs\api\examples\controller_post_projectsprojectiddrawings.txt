curl -i -X POST 'http://localhost:3080/v2/projects/db6a5cdc-2bed-4769-bc5e-3f260ca6d0ea/drawings' -d '{"svg": "<svg height=\"210\" width=\"500\"><line x1=\"0\" y1=\"0\" x2=\"200\" y2=\"200\" style=\"stroke:rgb(255,0,0);stroke-width:2\" /></svg>", "x": 10, "y": 20, "z": 0}'

POST /v2/projects/db6a5cdc-2bed-4769-bc5e-3f260ca6d0ea/drawings HTTP/1.1
{
    "svg": "<svg height=\"210\" width=\"500\"><line x1=\"0\" y1=\"0\" x2=\"200\" y2=\"200\" style=\"stroke:rgb(255,0,0);stroke-width:2\" /></svg>",
    "x": 10,
    "y": 20,
    "z": 0
}


HTTP/1.1 201
Connection: close
Content-Length: 344
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:57 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/drawings

{
    "drawing_id": "62afa856-4a43-4444-a376-60f6f963bb3d",
    "locked": false,
    "project_id": "db6a5cdc-2bed-4769-bc5e-3f260ca6d0ea",
    "rotation": 0,
    "svg": "<svg height=\"210\" width=\"500\"><line x1=\"0\" y1=\"0\" x2=\"200\" y2=\"200\" style=\"stroke:rgb(255,0,0);stroke-width:2\" /></svg>",
    "x": 10,
    "y": 20,
    "z": 0
}
