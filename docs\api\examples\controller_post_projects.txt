curl -i -X POST 'http://localhost:3080/v2/projects' -d '{"name": "test", "project_id": "10010203-0405-0607-0809-0a0b0c0d0e0f"}'

POST /v2/projects HTTP/1.1
{
    "name": "test",
    "project_id": "10010203-0405-0607-0809-0a0b0c0d0e0f"
}


HTTP/1.1 201
Connection: close
Content-Length: 560
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:00 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects

{
    "auto_close": true,
    "auto_open": false,
    "auto_start": false,
    "drawing_grid_size": 25,
    "filename": "test.gns3",
    "grid_size": 75,
    "name": "test",
    "path": "/tmp/tmp22gkofb4/projects/10010203-0405-0607-0809-0a0b0c0d0e0f",
    "project_id": "10010203-0405-0607-0809-0a0b0c0d0e0f",
    "scene_height": 1000,
    "scene_width": 2000,
    "show_grid": false,
    "show_interface_labels": false,
    "show_layers": false,
    "snap_to_grid": false,
    "status": "opened",
    "supplier": null,
    "variables": null,
    "zoom": 100
}
