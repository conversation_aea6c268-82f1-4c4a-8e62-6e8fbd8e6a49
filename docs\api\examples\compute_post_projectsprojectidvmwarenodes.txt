curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vmware/nodes' -d '{"linked_clone": false, "name": "VM1", "vmx_path": "/tmp/pytest-of-grossmj/pytest-41/test_vmware_create0/test.vmx"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vmware/nodes HTTP/1.1
{
    "linked_clone": false,
    "name": "VM1",
    "vmx_path": "/tmp/pytest-of-grossmj/pytest-41/test_vmware_create0/test.vmx"
}


HTTP/1.1 201
Connection: close
Content-Length: 611
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:28 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/vmware/nodes

{
    "adapter_type": "e1000",
    "adapters": 0,
    "console": 5004,
    "console_type": "telnet",
    "headless": false,
    "linked_clone": false,
    "name": "VM1",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/vmware/1008243f-21b0-4861-8f06-0622df304234",
    "node_id": "1008243f-21b0-4861-8f06-0622df304234",
    "on_close": "power_off",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "stopped",
    "usage": "",
    "use_any_adapter": false,
    "vmx_path": "/tmp/pytest-of-grossmj/pytest-41/test_vmware_create0/test.vmx"
}
