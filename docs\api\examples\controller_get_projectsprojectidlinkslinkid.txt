curl -i -X GET 'http://localhost:3080/v2/projects/1740ba78-ed59-4da9-880d-d2f4675a06c2/links/39a5ec94-aa23-4825-baad-853f0ac63e4a'

GET /v2/projects/1740ba78-ed59-4da9-880d-d2f4675a06c2/links/39a5ec94-aa23-4825-baad-853f0ac63e4a HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 921
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/links/{link_id}

{
    "capture_compute_id": null,
    "capture_file_name": null,
    "capture_file_path": null,
    "capturing": false,
    "filters": {},
    "link_id": "39a5ec94-aa23-4825-baad-853f0ac63e4a",
    "link_type": "ethernet",
    "nodes": [
        {
            "adapter_number": 0,
            "label": {
                "text": "Text",
                "x": 42,
                "y": 0
            },
            "node_id": "4a330d9b-fbb1-4865-ae79-2350e8ca9397",
            "port_number": 3
        },
        {
            "adapter_number": 2,
            "label": {
                "style": "font-family: TypeWriter;font-size: 10.0;font-weight: bold;fill: #000000;fill-opacity: 1.0;",
                "text": "2/4"
            },
            "node_id": "74ad52f6-faf2-4c6b-8b29-e36c9f0ba03a",
            "port_number": 4
        }
    ],
    "project_id": "1740ba78-ed59-4da9-880d-d2f4675a06c2",
    "suspend": false
}
