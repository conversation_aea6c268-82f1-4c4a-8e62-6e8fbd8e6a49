curl -i -X PUT 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/qemu/nodes/93352cad-8f0c-42d6-a4bf-72a48c3d2bff' -d '{"console": 5006, "hdb_disk_image": "linux\u8f7d.img", "name": "test", "ram": 1024}'

PUT /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/qemu/nodes/93352cad-8f0c-42d6-a4bf-72a48c3d2bff HTTP/1.1
{
    "console": 5006,
    "hdb_disk_image": "linux\u8f7d.img",
    "name": "test",
    "ram": 1024
}


HTTP/1.1 200
Connection: close
Content-Length: 1422
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:42 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/qemu/nodes/{node_id}

{
    "adapter_type": "e1000",
    "adapters": 1,
    "bios_image": "",
    "bios_image_md5sum": null,
    "boot_priority": "c",
    "cdrom_image": "",
    "cdrom_image_md5sum": null,
    "command_line": "",
    "console": 5006,
    "console_type": "telnet",
    "cpu_throttling": 0,
    "cpus": 1,
    "hda_disk_image": "",
    "hda_disk_image_md5sum": null,
    "hda_disk_interface": "ide",
    "hdb_disk_image": "linux\u8f7d.img",
    "hdb_disk_image_md5sum": "c4ca4238a0b923820dcc509a6f75849b",
    "hdb_disk_interface": "ide",
    "hdc_disk_image": "",
    "hdc_disk_image_md5sum": null,
    "hdc_disk_interface": "ide",
    "hdd_disk_image": "",
    "hdd_disk_image_md5sum": null,
    "hdd_disk_interface": "ide",
    "initrd": "",
    "initrd_md5sum": null,
    "kernel_command_line": "",
    "kernel_image": "",
    "kernel_image_md5sum": null,
    "legacy_networking": false,
    "mac_address": "0c:dd:80:2b:ff:00",
    "name": "test",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/qemu/93352cad-8f0c-42d6-a4bf-72a48c3d2bff",
    "node_id": "93352cad-8f0c-42d6-a4bf-72a48c3d2bff",
    "on_close": "power_off",
    "options": "",
    "platform": "x86_64",
    "process_priority": "low",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "qemu_path": "/tmp/tmphb4tqqk2/qemu-system-x86_64",
    "ram": 1024,
    "status": "stopped",
    "usage": ""
}
