curl -i -X PUT 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/traceng/nodes/5656a9e1-993c-478d-8987-3755026eab7a' -d '{"ip_address": "***********", "name": "test"}'

PUT /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/traceng/nodes/5656a9e1-993c-478d-8987-3755026eab7a HTTP/1.1
{
    "ip_address": "***********",
    "name": "test"
}


HTTP/1.1 200
Connection: close
Content-Length: 444
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:27 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/traceng/nodes/{node_id}

{
    "command_line": "",
    "console": null,
    "console_type": "none",
    "default_destination": "",
    "ip_address": "***********",
    "name": "test",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/traceng/5656a9e1-993c-478d-8987-3755026eab7a",
    "node_id": "5656a9e1-993c-478d-8987-3755026eab7a",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "stopped"
}
