curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/nat/nodes' -d '{"name": "Nat 1"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/nat/nodes HTTP/1.1
{
    "name": "Nat 1"
}


HTTP/1.1 201
Connection: close
Content-Length: 335
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:09 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/nat/nodes

{
    "name": "Nat 1",
    "node_id": "4b2c0443-0726-4507-a30b-7ac535b1bbaf",
    "ports_mapping": [
        {
            "interface": "virbr0",
            "name": "nat0",
            "port_number": 0,
            "type": "ethernet"
        }
    ],
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "started"
}
