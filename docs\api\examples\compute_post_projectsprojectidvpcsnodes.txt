curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vpcs/nodes' -d '{"name": "PC TEST 1"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vpcs/nodes HTTP/1.1
{
    "name": "PC TEST 1"
}


HTTP/1.1 201
Connection: close
Content-Length: 384
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:47 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/vpcs/nodes

{
    "command_line": "",
    "console": 5004,
    "console_type": "telnet",
    "name": "PC TEST 1",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/vpcs/9ba731c6-acdd-479c-9d95-c464912df54e",
    "node_id": "9ba731c6-acdd-479c-9d95-c464912df54e",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "stopped"
}
