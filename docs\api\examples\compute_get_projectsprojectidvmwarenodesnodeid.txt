curl -i -X GET 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vmware/nodes/716f8727-4421-4ba9-9e24-031505533c36'

GET /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vmware/nodes/716f8727-4421-4ba9-9e24-031505533c36 HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 611
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:29 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/vmware/nodes/{node_id}

{
    "adapter_type": "e1000",
    "adapters": 0,
    "console": 5004,
    "console_type": "telnet",
    "headless": false,
    "linked_clone": false,
    "name": "VMTEST",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/vmware/716f8727-4421-4ba9-9e24-031505533c36",
    "node_id": "716f8727-4421-4ba9-9e24-031505533c36",
    "on_close": "power_off",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "stopped",
    "usage": "",
    "use_any_adapter": false,
    "vmx_path": "/tmp/pytest-of-grossmj/pytest-41/test_vmware_get0/test.vmx"
}
