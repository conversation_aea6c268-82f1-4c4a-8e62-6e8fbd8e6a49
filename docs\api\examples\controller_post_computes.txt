curl -i -X POST 'http://localhost:3080/v2/computes' -d '{"compute_id": "my_compute_id", "host": "localhost", "password": "secure", "port": 84, "protocol": "http", "user": "julien"}'

POST /v2/computes HTTP/1.1
{
    "compute_id": "my_compute_id",
    "host": "localhost",
    "password": "secure",
    "port": 84,
    "protocol": "http",
    "user": "julien"
}


HTTP/1.1 201
Connection: close
Content-Length: 358
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:50 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/computes

{
    "capabilities": {
        "node_types": [],
        "version": null
    },
    "compute_id": "my_compute_id",
    "connected": false,
    "cpu_usage_percent": null,
    "host": "localhost",
    "last_error": null,
    "memory_usage_percent": null,
    "name": "http://julien@localhost:84",
    "port": 84,
    "protocol": "http",
    "user": "julien"
}
