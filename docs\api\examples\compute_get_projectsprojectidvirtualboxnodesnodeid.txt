curl -i -X GET 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/virtualbox/nodes/bcd3206f-3edd-4b44-b5c8-e40f4a5c8909'

GET /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/virtualbox/nodes/bcd3206f-3edd-4b44-b5c8-e40f4a5c8909 HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 483
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:27 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/virtualbox/nodes/{node_id}

{
    "adapter_type": "Intel PRO/1000 MT Desktop (82540EM)",
    "adapters": 0,
    "console": 5004,
    "console_type": "telnet",
    "headless": false,
    "linked_clone": false,
    "name": "VMTEST",
    "node_directory": null,
    "node_id": "bcd3206f-3edd-4b44-b5c8-e40f4a5c8909",
    "on_close": "power_off",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "ram": 0,
    "status": "stopped",
    "usage": "",
    "use_any_adapter": false,
    "vmname": "VMTEST"
}
