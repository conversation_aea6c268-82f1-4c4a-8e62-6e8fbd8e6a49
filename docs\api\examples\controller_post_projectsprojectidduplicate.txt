curl -i -X POST 'http://localhost:3080/v2/projects/52bc51e0-e61a-4894-af60-88d46bf03f8f/duplicate' -d '{"name": "hello"}'

POST /v2/projects/52bc51e0-e61a-4894-af60-88d46bf03f8f/duplicate HTTP/1.1
{
    "name": "hello"
}


HTTP/1.1 201
Connection: close
Content-Length: 562
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:35 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/duplicate

{
    "auto_close": true,
    "auto_open": false,
    "auto_start": false,
    "drawing_grid_size": 25,
    "filename": "hello.gns3",
    "grid_size": 75,
    "name": "hello",
    "path": "/tmp/tmpt8_8zdrf/projects/d25d6494-5b3d-42bf-871e-11880946c447",
    "project_id": "d25d6494-5b3d-42bf-871e-11880946c447",
    "scene_height": 1000,
    "scene_width": 2000,
    "show_grid": false,
    "show_interface_labels": false,
    "show_layers": false,
    "snap_to_grid": false,
    "status": "closed",
    "supplier": null,
    "variables": null,
    "zoom": 100
}
