curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/virtualbox/nodes' -d '{"linked_clone": false, "name": "VM1", "vmname": "VM1"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/virtualbox/nodes HTTP/1.1
{
    "linked_clone": false,
    "name": "VM1",
    "vmname": "VM1"
}


HTTP/1.1 201
Connection: close
Content-Length: 477
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:27 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/virtualbox/nodes

{
    "adapter_type": "Intel PRO/1000 MT Desktop (82540EM)",
    "adapters": 0,
    "console": 5004,
    "console_type": "telnet",
    "headless": false,
    "linked_clone": false,
    "name": "VM1",
    "node_directory": null,
    "node_id": "e7a8f831-0fb0-4946-a188-d3ac4112e097",
    "on_close": "power_off",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "ram": 0,
    "status": "stopped",
    "usage": "",
    "use_any_adapter": false,
    "vmname": "VM1"
}
