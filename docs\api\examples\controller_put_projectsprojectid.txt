curl -i -X PUT 'http://localhost:3080/v2/projects/10010203-0405-0607-0809-0a0b0c0d0e0f' -d '{"name": "test2"}'

PUT /v2/projects/10010203-0405-0607-0809-0a0b0c0d0e0f HTTP/1.1
{
    "name": "test2"
}


HTTP/1.1 200
Connection: close
Content-Length: 692
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:01 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}

{
    "auto_close": true,
    "auto_open": false,
    "auto_start": false,
    "drawing_grid_size": 25,
    "filename": "test.gns3",
    "grid_size": 75,
    "name": "test2",
    "path": "/tmp/tmp4p7e6dvr/projects/10010203-0405-0607-0809-0a0b0c0d0e0f",
    "project_id": "10010203-0405-0607-0809-0a0b0c0d0e0f",
    "scene_height": 1000,
    "scene_width": 2000,
    "show_grid": false,
    "show_interface_labels": false,
    "show_layers": false,
    "snap_to_grid": false,
    "status": "opened",
    "supplier": null,
    "variables": [
        {
            "name": "TEST1"
        },
        {
            "name": "TEST2",
            "value": "value1"
        }
    ],
    "zoom": 100
}
