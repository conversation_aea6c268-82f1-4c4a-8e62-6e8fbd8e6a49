[Server]
; IP where the server listen for connections
host = 0.0.0.0
; HTTP port for controlling the servers
port = 3080

; Option to enable SSL encryption
ssl = False
certfile=/home/<USER>/.config/GNS3/ssl/server.cert
certkey=/home/<USER>/.config/GNS3/ssl/server.key

; Path where binary images are stored
images_path = /home/<USER>/GNS3/images

; Path where user projects are stored
projects_path = /home/<USER>/GNS3/projects

; Path where custom user appliances are stored
appliances_path = /home/<USER>/GNS3/appliances

; Path where custom user symbols are stored
symbols_path = /home/<USER>/GNS3/symbols

; Path where files like built-in appliances and Docker resources are stored
; The default path is the local user data directory
; (Linux: "~/.local/share/GNS3", macOS: "~/Library/Application Support/GNS3", Windows: "%APPDATA%\GNS3")
; resources_path = /home/<USER>/GNS3/resources

; Option to automatically send crash reports to the GNS3 team
report_errors = True

; First console port of the range allocated to devices
console_start_port_range = 5000
; Last console port of the range allocated to devices
console_end_port_range = 10000

; First VNC console port of the range allocated to devices.
; The value MUST BE >= 5900 and <= 65535
vnc_console_start_port_range = 5900
; Last VNC console port of the range allocated to devices
; The value MUST BE >= 5900 and <= 65535
vnc_console_end_port_range = 10000

; First port of the range allocated for inter-device communication. Two ports are allocated per link.
udp_start_port_range = 20000
; Last port of the range allocated for inter-device communication. Two ports are allocated per link
udp_end_port_range = 30000

; uBridge executable location, default: search in PATH
;ubridge_path = ubridge

; Option to enable HTTP authentication.
auth = False
; Username for HTTP authentication.
user = gns3
; Password for HTTP authentication.
password = gns3

; Only allow these interfaces to be used by GNS3, for the Cloud node for example (Linux/OSX only)
; Do not forget to allow virbr0 in order for the NAT node to work
allowed_interfaces = eth0,eth1,virbr0

; Specify the NAT interface to be used by the NAT node
; Default is virbr0 on Linux (requires libvirt) and vmnet8 for other platforms (requires VMware)
default_nat_interface = vmnet10

; Enable the built-in templates
enable_builtin_templates = True

; Install built-in appliances
install_builtin_appliances = True

; check if hardware virtualization is used by other emulators (KVM, VMware or VirtualBox)
hardware_virtualization_check = True

[VPCS]
; VPCS executable location, default: search in PATH
;vpcs_path = vpcs

[Dynamips]
; Enable auxiliary console ports on IOS routers
allocate_aux_console_ports = False
mmap_support = True
; Dynamips executable path, default: search in PATH
;dynamips_path = dynamips
sparse_memory_support = True
ghost_ios_support = True

[IOU]
; Path of your .iourc file. If not provided, the file is searched in $HOME/.iourc
iourc_path = /home/<USER>/.iourc
; Validate if the iourc license file is correct. If you turn this off and your licence is invalid IOU will not start and no errors will be shown.
license_check = True

[Qemu]
; !! Remember to add the gns3 user to the KVM group, otherwise you will not have read / write permissions to /dev/kvm !! (Linux only, has priority over enable_hardware_acceleration)
enable_kvm = True
; Require KVM to be installed in order to start VMs (Linux only, has priority over require_hardware_acceleration)
require_kvm = True
; Enable hardware acceleration (all platforms)
enable_hardware_acceleration = True
; Require hardware acceleration in order to start VMs (all platforms)
require_hardware_acceleration = False
; Allow unsafe additional command line options
allow_unsafe_options = False

[VMware]
; First vmnet interface of the range that can be managed by the GNS3 server
vmnet_start_range = 2
; Last vmnet interface of the range that can be managed by the GNS3 server. It must be maximum 19 on Windows.
vmnet_end_range = 255
