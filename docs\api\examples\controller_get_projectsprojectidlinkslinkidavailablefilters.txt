curl -i -X GET 'http://localhost:3080/v2/projects/b681af76-0962-4753-97ee-4975655dfef5/links/fce3b8b3-0aae-490b-b1b8-077257a252a5/available_filters'

GET /v2/projects/b681af76-0962-4753-97ee-4975655dfef5/links/fce3b8b3-0aae-490b-b1b8-077257a252a5/available_filters HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 2119
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/links/{link_id}/available_filters

[
    {
        "description": "It will drop everything with a -1 frequency, drop every Nth packet with a positive frequency, or drop nothing",
        "name": "Frequency drop",
        "parameters": [
            {
                "maximum": 32767,
                "minimum": -1,
                "name": "Frequency",
                "type": "int",
                "unit": "th packet"
            }
        ],
        "type": "frequency_drop"
    },
    {
        "description": "The percentage represents the chance for a packet to be lost",
        "name": "Packet loss",
        "parameters": [
            {
                "maximum": 100,
                "minimum": 0,
                "name": "Chance",
                "type": "int",
                "unit": "%"
            }
        ],
        "type": "packet_loss"
    },
    {
        "description": "Delay packets in milliseconds. You can add jitter in milliseconds (+/-) of the delay",
        "name": "Delay",
        "parameters": [
            {
                "maximum": 32767,
                "minimum": 0,
                "name": "Latency",
                "type": "int",
                "unit": "ms"
            },
            {
                "maximum": 32767,
                "minimum": 0,
                "name": "Jitter (-/+)",
                "type": "int",
                "unit": "ms"
            }
        ],
        "type": "delay"
    },
    {
        "description": "The percentage represents the chance for a packet to be corrupted",
        "name": "Corrupt",
        "parameters": [
            {
                "maximum": 100,
                "minimum": 0,
                "name": "Chance",
                "type": "int",
                "unit": "%"
            }
        ],
        "type": "corrupt"
    },
    {
        "description": "This filter will drop any packet matching a BPF expression. Put one expression per line",
        "name": "Berkeley Packet Filter (BPF)",
        "parameters": [
            {
                "name": "Filters",
                "type": "text"
            }
        ],
        "type": "bpf"
    }
]
