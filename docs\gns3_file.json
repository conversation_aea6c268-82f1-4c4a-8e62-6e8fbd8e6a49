{"$schema": "http://json-schema.org/draft-04/schema#", "description": "The topology", "type": "object", "properties": {"project_id": {"description": "Project UUID", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "type": {"description": "Type of file. It's always topology", "enum": ["topology"]}, "auto_start": {"description": "Start the topology when opened", "type": "boolean"}, "auto_close": {"description": "Close the topology when no client is connected", "type": "boolean"}, "auto_open": {"description": "Open the topology with GNS3", "type": "boolean"}, "revision": {"description": "Version of the .gns3 specification.", "type": "integer"}, "version": {"description": "Version of the GNS3 software which have update the file for the last time", "type": "string"}, "name": {"type": "string", "description": "Name of the project"}, "scene_height": {"type": "integer", "description": "Height of the drawing area"}, "scene_width": {"type": "integer", "description": "Width of the drawing area"}, "zoom": {"type": "integer", "description": "Zoom of the drawing area"}, "show_layers": {"type": "boolean", "description": "Show layers on the drawing area"}, "snap_to_grid": {"type": "boolean", "description": "Snap to grid on the drawing area"}, "show_grid": {"type": "boolean", "description": "Show the grid on the drawing area"}, "grid_size": {"type": "integer", "description": "Grid size for the drawing area for nodes"}, "drawing_grid_size": {"type": "integer", "description": "Grid size for the drawing area for drawings"}, "show_interface_labels": {"type": "boolean", "description": "Show interface labels on the drawing area"}, "supplier": {"type": ["object", "null"], "description": "Supplier of the project", "properties": {"logo": {"type": "string", "description": "Path to the project supplier logo"}, "url": {"type": "string", "description": "URL to the project supplier site"}}}, "variables": {"type": ["array", "null"], "description": "Variables required to run the project", "items": {"properties": {"name": {"type": "string", "description": "Variable name", "minLength": 1}, "value": {"type": "string", "description": "Variable value"}}, "required": ["name"]}}, "topology": {"description": "The topology content", "type": "object", "properties": {"computes": {"description": "Computes servers", "type": "array", "items": {"$schema": "http://json-schema.org/draft-04/schema#", "description": "Request validation to a GNS3 compute object instance", "type": "object", "properties": {"compute_id": {"description": "Server identifier", "type": "string"}, "name": {"description": "Server name", "type": "string"}, "protocol": {"description": "Server protocol", "enum": ["http", "https"]}, "host": {"description": "Server host", "type": "string"}, "port": {"description": "Server port", "type": "integer"}, "user": {"description": "User for authentication", "type": ["string", "null"]}, "connected": {"description": "Whether the controller is connected to the compute or not", "type": "boolean"}, "cpu_usage_percent": {"description": "CPU usage of the compute. Read only", "type": ["number", "null"], "maximum": 100, "minimum": 0}, "memory_usage_percent": {"description": "RAM usage of the compute. Read only", "type": ["number", "null"], "maximum": 100, "minimum": 0}, "last_error": {"description": "Last error on the compute", "type": ["string", "null"]}, "capabilities": {"$schema": "http://json-schema.org/draft-04/schema#", "description": "Get what a server support", "type": "object", "required": ["version", "node_types"], "properties": {"version": {"description": "Version number", "type": ["string", "null"]}, "node_types": {"type": "array", "items": {"description": "Type of node", "enum": ["cloud", "nat", "ethernet_hub", "ethernet_switch", "frame_relay_switch", "atm_switch", "docker", "dynamips", "vpcs", "traceng", "virtualbox", "vmware", "iou", "qemu"]}, "description": "Node type supported by the compute"}, "platform": {"type": "string", "description": "Platform where the compute is running"}}, "additionalProperties": false}}, "additionalProperties": false, "required": ["compute_id", "protocol", "host", "port", "name"]}}, "drawings": {"description": "Drawings elements", "type": "array", "items": {"$schema": "http://json-schema.org/draft-04/schema#", "description": "An drawing object", "type": "object", "properties": {"drawing_id": {"description": "Drawing UUID", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "project_id": {"description": "Project UUID", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "x": {"description": "X property", "type": "integer"}, "y": {"description": "Y property", "type": "integer"}, "z": {"description": "Z property", "type": "integer"}, "locked": {"description": "Whether the element locked or not", "type": "boolean"}, "rotation": {"description": "Rotation of the element", "type": "integer", "minimum": -359, "maximum": 360}, "svg": {"description": "SVG content of the drawing", "type": "string"}}, "additionalProperties": false}}, "links": {"description": "Link elements", "type": "array", "items": {"$schema": "http://json-schema.org/draft-04/schema#", "description": "A link object", "type": "object", "properties": {"link_id": {"description": "Link UUID", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "project_id": {"description": "Project UUID", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "nodes": {"description": "List of the VMS", "type": "array", "items": {"type": "object", "properties": {"node_id": {"description": "Node UUID", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "adapter_number": {"description": "Adapter number", "type": "integer"}, "port_number": {"description": "Port number", "type": "integer"}, "label": {"type": "object", "properties": {"text": {"type": "string"}, "style": {"description": "SVG style attribute. Apply default style if null", "type": ["string", "null"]}, "x": {"description": "Relative X position of the label. Center it if null", "type": ["integer", "null"]}, "y": {"description": "Relative Y position of the label", "type": "integer"}, "rotation": {"description": "Rotation of the label", "type": "integer", "minimum": -359, "maximum": 360}}, "required": ["text"], "additionalProperties": false}}, "required": ["node_id", "adapter_number", "port_number"], "additionalProperties": false}}, "suspend": {"type": "boolean", "description": "Suspend the link"}, "link_style": {"type": "object", "description": "Link line style", "items": {"type": "object", "properties": {"color": {"description": "Link line color", "type": "string"}, "width": {"description": "Link line width", "type": "integer"}, "type": {"description": "Link line type", "type": "integer"}}}}, "filters": {"$schema": "http://json-schema.org/draft-04/schema#", "description": "Packet filter. This allow to simulate latency and errors", "type": "object"}, "capturing": {"description": "Read only property. True if a capture running on the link", "type": "boolean"}, "capture_file_name": {"description": "Read only property. The name of the capture file if a capture is running", "type": ["string", "null"]}, "capture_file_path": {"description": "Read only property. The full path of the capture file if a capture is running", "type": ["string", "null"]}, "capture_compute_id": {"description": "Read only property. The compute identifier where a capture is running", "type": ["string", "null"]}, "link_type": {"description": "Type of link", "enum": ["ethernet", "serial"]}}, "additionalProperties": false}}, "nodes": {"description": "Nodes elements", "type": "array", "items": {"$schema": "http://json-schema.org/draft-04/schema#", "description": "A node object", "type": "object", "properties": {"compute_id": {"description": "Compute identifier", "type": "string"}, "project_id": {"description": "Project UUID", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "node_id": {"description": "Node UUID", "type": "string", "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "template_id": {"description": "Template UUID from which the node has been created. Read only", "type": ["null", "string"], "minLength": 36, "maxLength": 36, "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"}, "node_type": {"description": "Type of node", "enum": ["cloud", "nat", "ethernet_hub", "ethernet_switch", "frame_relay_switch", "atm_switch", "docker", "dynamips", "vpcs", "traceng", "virtualbox", "vmware", "iou", "qemu"]}, "node_directory": {"description": "Working directory of the node. Read only", "type": ["null", "string"]}, "command_line": {"description": "Command line use to start the node", "type": ["null", "string"]}, "name": {"description": "Node name", "type": "string", "minLength": 1}, "console": {"description": "Console TCP port", "minimum": 1, "maximum": 65535, "type": ["integer", "null"]}, "console_host": {"description": "Console host. Warning if the host is 0.0.0.0 or :: (listen on all interfaces) you need to use the same address you use to connect to the controller.", "type": "string", "minLength": 1}, "console_type": {"description": "Console type", "enum": ["vnc", "telnet", "http", "https", "spice", "spice+agent", "none", null]}, "console_auto_start": {"description": "Automatically start the console when the node has started", "type": "boolean"}, "properties": {"description": "Properties specific to an emulator", "type": "object"}, "status": {"description": "Status of the node", "enum": ["stopped", "started", "suspended"]}, "label": {"type": "object", "properties": {"text": {"type": "string"}, "style": {"description": "SVG style attribute. Apply default style if null", "type": ["string", "null"]}, "x": {"description": "Relative X position of the label. Center it if null", "type": ["integer", "null"]}, "y": {"description": "Relative Y position of the label", "type": "integer"}, "rotation": {"description": "Rotation of the label", "type": "integer", "minimum": -359, "maximum": 360}}, "required": ["text"], "additionalProperties": false}, "symbol": {"description": "Symbol of the node", "type": ["string", "null"], "minLength": 1}, "width": {"description": "Width of the node (Read only)", "type": "integer"}, "height": {"description": "Height of the node (Read only)", "type": "integer"}, "x": {"description": "X position of the node", "type": "integer"}, "y": {"description": "Y position of the node", "type": "integer"}, "z": {"description": "Z position of the node", "type": "integer"}, "locked": {"description": "Whether the element locked or not", "type": "boolean"}, "port_name_format": {"description": "Formating for port name {0} will be replace by port number", "type": "string"}, "port_segment_size": {"description": "Size of the port segment", "type": "integer", "minimum": 0}, "first_port_name": {"description": "Name of the first port", "type": ["string", "null"]}, "custom_adapters": {"type": "array", "default": [], "items": {"type": "object", "description": "Custom properties", "properties": {"adapter_number": {"type": "integer", "description": "Adapter number"}, "port_name": {"type": "string", "description": "Custom port name", "minLength": 1}, "adapter_type": {"type": "string", "description": "Custom adapter type", "minLength": 1}, "mac_address": {"description": "Custom MAC address", "type": "string", "minLength": 1, "pattern": "^([0-9a-fA-F]{2}[:]){5}([0-9a-fA-F]{2})$"}}, "additionalProperties": false, "required": ["adapter_number"]}}, "ports": {"description": "List of node ports READ only", "type": "array", "items": {"type": "object", "description": "A node port", "properties": {"name": {"type": "string", "description": "Port name"}, "short_name": {"type": "string", "description": "Short version of port name"}, "adapter_number": {"type": "integer", "description": "Adapter slot"}, "adapter_type": {"description": "Adapter type", "type": ["string", "null"], "minLength": 1}, "port_number": {"type": "integer", "description": "Port slot"}, "link_type": {"description": "Type of link", "enum": ["ethernet", "serial"]}, "data_link_types": {"type": "object", "description": "Available PCAP types for capture", "properties": {}}, "mac_address": {"description": "MAC address (if available)", "type": ["string", "null"], "minLength": 1, "pattern": "^([0-9a-fA-F]{2}[:]){5}([0-9a-fA-F]{2})$"}}, "additionalProperties": false}}}, "additionalProperties": false, "required": ["name", "node_type", "compute_id"]}}}, "required": ["nodes", "links", "drawings", "computes"], "additionalProperties": false}}, "required": ["project_id", "type", "revision", "version", "name", "topology"], "additionalProperties": false}