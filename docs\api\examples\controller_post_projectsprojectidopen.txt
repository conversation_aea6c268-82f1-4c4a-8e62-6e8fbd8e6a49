curl -i -X POST 'http://localhost:3080/v2/projects/8094bba2-62bb-48a5-ae9a-b977da5b7cb2/open' -d '{}'

POST /v2/projects/8094bba2-62bb-48a5-ae9a-b977da5b7cb2/open HTTP/1.1
{}


HTTP/1.1 201
Connection: close
Content-Length: 560
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:01 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/open

{
    "auto_close": true,
    "auto_open": false,
    "auto_start": false,
    "drawing_grid_size": 25,
    "filename": "test.gns3",
    "grid_size": 75,
    "name": "test",
    "path": "/tmp/tmp7b2lfg8t/projects/8094bba2-62bb-48a5-ae9a-b977da5b7cb2",
    "project_id": "8094bba2-62bb-48a5-ae9a-b977da5b7cb2",
    "scene_height": 1000,
    "scene_width": 2000,
    "show_grid": false,
    "show_interface_labels": false,
    "show_layers": false,
    "snap_to_grid": false,
    "status": "opened",
    "supplier": null,
    "variables": null,
    "zoom": 100
}
