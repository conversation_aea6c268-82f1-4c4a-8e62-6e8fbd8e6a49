curl -i -X GET 'http://localhost:3080/v2/projects/51c1ac28-1756-4912-a31a-36d480326e9b/links'

GET /v2/projects/51c1ac28-1756-4912-a31a-36d480326e9b/links HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 1251
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/links

[
    {
        "capture_compute_id": null,
        "capture_file_name": null,
        "capture_file_path": null,
        "capturing": false,
        "filters": {
            "frequency_drop": [
                50
            ],
            "latency": [
                10
            ]
        },
        "link_id": "cff847b1-0233-4ee1-bb26-82039debff2b",
        "link_type": "ethernet",
        "nodes": [
            {
                "adapter_number": 0,
                "label": {
                    "style": "font-family: TypeWriter;font-size: 10.0;font-weight: bold;fill: #000000;fill-opacity: 1.0;",
                    "text": "0/3"
                },
                "node_id": "fd4dc1ea-7688-4e42-bba1-71bfa31eddbc",
                "port_number": 3
            },
            {
                "adapter_number": 2,
                "label": {
                    "style": "font-family: TypeWriter;font-size: 10.0;font-weight: bold;fill: #000000;fill-opacity: 1.0;",
                    "text": "2/4"
                },
                "node_id": "74e807d2-2e9a-467c-a44f-08b725c24cc9",
                "port_number": 4
            }
        ],
        "project_id": "51c1ac28-1756-4912-a31a-36d480326e9b",
        "suspend": false
    }
]
