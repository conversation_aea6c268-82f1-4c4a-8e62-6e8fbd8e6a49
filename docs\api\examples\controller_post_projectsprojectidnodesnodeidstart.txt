curl -i -X POST 'http://localhost:3080/v2/projects/2dc05e54-1f48-4d84-86fc-ccfe9a3e9cb1/nodes/7e3eb168-52c3-41ec-bd0e-5a90fabf9cf9/start' -d '{}'

POST /v2/projects/2dc05e54-1f48-4d84-86fc-ccfe9a3e9cb1/nodes/7e3eb168-52c3-41ec-bd0e-5a90fabf9cf9/start HTTP/1.1
{}


HTTP/1.1 200
Connection: close
Content-Length: 1158
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:59 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/nodes/{node_id}/start

{
    "command_line": null,
    "compute_id": "example.com",
    "console": null,
    "console_auto_start": false,
    "console_host": "<MagicMock name='mock.console_host' id='140147697458648'>",
    "console_type": null,
    "custom_adapters": [],
    "first_port_name": null,
    "height": 59,
    "label": {
        "rotation": 0,
        "style": null,
        "text": "test",
        "x": null,
        "y": -40
    },
    "locked": false,
    "name": "test",
    "node_directory": null,
    "node_id": "7e3eb168-52c3-41ec-bd0e-5a90fabf9cf9",
    "node_type": "vpcs",
    "port_name_format": "Ethernet{0}",
    "port_segment_size": 0,
    "ports": [
        {
            "adapter_number": 0,
            "data_link_types": {
                "Ethernet": "DLT_EN10MB"
            },
            "link_type": "ethernet",
            "name": "Ethernet0",
            "port_number": 0,
            "short_name": "e0"
        }
    ],
    "project_id": "2dc05e54-1f48-4d84-86fc-ccfe9a3e9cb1",
    "properties": {},
    "status": "stopped",
    "symbol": ":/symbols/computer.svg",
    "template_id": null,
    "width": 65,
    "x": 0,
    "y": 0,
    "z": 1
}
