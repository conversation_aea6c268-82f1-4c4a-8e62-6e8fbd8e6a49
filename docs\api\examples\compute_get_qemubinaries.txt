curl -i -X GET 'http://localhost:3080/v2/compute/qemu/binaries' -d '{"archs": ["i386"]}'

GET /v2/compute/qemu/binaries HTTP/1.1
{
    "archs": [
        "i386"
    ]
}


HTTP/1.1 200
Connection: close
Content-Length: 212
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:55 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/qemu/binaries

[
    {
        "path": "/tmp/x86_64",
        "version": "2.2.0"
    },
    {
        "path": "/tmp/alpha",
        "version": "2.1.0"
    },
    {
        "path": "/tmp/i386",
        "version": "2.1.0"
    }
]
