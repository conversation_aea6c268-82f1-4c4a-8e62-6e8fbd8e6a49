curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/iou/nodes/12cd096f-f0c1-45f9-ac57-360f1d379c48/duplicate' -d '{"destination_node_id": "3949cfaa-2574-4064-a088-5f48ae784e9b"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/iou/nodes/12cd096f-f0c1-45f9-ac57-360f1d379c48/duplicate HTTP/1.1
{
    "destination_node_id": "3949cfaa-2574-4064-a088-5f48ae784e9b"
}


HTTP/1.1 201
Connection: close
Content-Length: 4
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:09 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/iou/nodes/{node_id}/duplicate

true
