curl -i -X GET 'http://localhost:3080/v2/projects/de7a5304-5089-4d87-b131-ab463aa9d708/drawings'

GET /v2/projects/de7a5304-5089-4d87-b131-ab463aa9d708/drawings HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 388
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:57 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/drawings

[
    {
        "drawing_id": "2ebb202d-7cd6-4e0e-8448-736f6aa9c873",
        "locked": false,
        "project_id": "de7a5304-5089-4d87-b131-ab463aa9d708",
        "rotation": 0,
        "svg": "<svg height=\"210\" width=\"500\"><line x1=\"0\" y1=\"0\" x2=\"200\" y2=\"200\" style=\"stroke:rgb(255,0,0);stroke-width:2\" /></svg>",
        "x": 10,
        "y": 20,
        "z": 0
    }
]
