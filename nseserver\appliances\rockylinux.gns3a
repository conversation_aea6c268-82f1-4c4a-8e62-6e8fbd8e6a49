{"appliance_id": "3c38885f-71c3-4d9c-976c-cf5515b1c875", "name": "RockyLinux", "category": "guest", "description": "Rocky Linux is a community enterprise operating system designed to be 100% bug-for-bug compatible with Red Hat Enterprise Linux (RHEL).", "vendor_name": "Rocky Enterprise Software Foundation", "vendor_url": "https://rockylinux.org", "documentation_url": "https://docs.rockylinux.org", "product_name": "Rocky <PERSON>", "registry_version": 4, "status": "stable", "maintainer": "Da<PERSON><PERSON><PERSON>", "maintainer_email": "<EMAIL>", "usage": "Username:\trocky\nPassword:\trocky\nTo become root, use \"sudo -i\".\n\nTo improve performance, increase RAM and vCPUs in the VM settings.", "symbol": "linux_guest.svg", "port_name_format": "ens{port4}", "qemu": {"adapter_type": "virtio-net-pci", "adapters": 1, "ram": 1024, "hda_disk_interface": "virtio", "arch": "x86_64", "console_type": "telnet", "boot_priority": "c", "kvm": "require", "options": "-nographic -cpu host"}, "images": [{"filename": "Rocky-9-GenericCloud-Base-9.5-20241118.0.x86_64.qcow2", "version": "9.5", "md5sum": "880eccf788301bb9f34669faebe09276", "filesize": 609812480, "download_url": "https://download.rockylinux.org/pub/rocky/9/images/x86_64/", "direct_download_url": "https://download.rockylinux.org/pub/rocky/9/images/x86_64/Rocky-9-GenericCloud-Base-9.5-20241118.0.x86_64.qcow2"}, {"filename": "Rocky-9-GenericCloud-Base-9.3-20231113.0.x86_64.qcow2", "version": "9.3", "md5sum": "48cdeb033364af5909e15ee48d0e144d", "filesize": 1083965440, "download_url": "https://download.rockylinux.org/pub/rocky/9/images/x86_64/", "direct_download_url": "https://download.rockylinux.org/pub/rocky/9/images/x86_64/Rocky-9-GenericCloud-Base-9.3-20231113.0.x86_64.qcow2"}, {"filename": "Rocky-9-GenericCloud-Base-9.2-20230513.0.x86_64.qcow2", "version": "9.2", "md5sum": "2022bdb49a691119f1fd3cc76de0a846", "filesize": 989265920, "download_url": "https://download.rockylinux.org/pub/rocky/9/images/x86_64/", "direct_download_url": "https://download.rockylinux.org/pub/rocky/9/images/x86_64/Rocky-9-GenericCloud-Base-9.2-20230513.0.x86_64.qcow2"}, {"filename": "Rocky-8-GenericCloud-Base-8.9-20231119.0.x86_64.qcow2", "version": "8.9", "md5sum": "50c44d8d9c4df43694372c13768f114c", "filesize": 1971978240, "download_url": "https://download.rockylinux.org/pub/rocky/8/images/x86_64/", "direct_download_url": "https://download.rockylinux.org/pub/rocky/8/images/x86_64/Rocky-8-GenericCloud-Base-8.9-20231119.0.x86_64.qcow2"}, {"filename": "Rocky-8-GenericCloud-Base-8.8-20230518.0.x86_64.qcow2", "version": "8.8", "md5sum": "3ad7d355909cc37100c037562e4b3b6d", "filesize": 1800536064, "download_url": "https://download.rockylinux.org/pub/rocky/8/images/x86_64/", "direct_download_url": "https://download.rockylinux.org/pub/rocky/8/images/x86_64/Rocky-8-GenericCloud-Base-8.8-20230518.0.x86_64.qcow2"}, {"filename": "rocky-cloud-init-data.iso", "version": "1.0", "md5sum": "33ffda3a81436e305f37fb913edd6d43", "filesize": 374784, "download_url": "https://github.com/GNS3/gns3-registry/tree/master/cloud-init/rocky-cloud", "direct_download_url": "https://github.com/GNS3/gns3-registry/raw/master/cloud-init/rocky-cloud/rocky-cloud-init-data.iso"}], "versions": [{"name": "9.5", "images": {"hda_disk_image": "Rocky-9-GenericCloud-Base-9.5-20241118.0.x86_64.qcow2", "cdrom_image": "rocky-cloud-init-data.iso"}}, {"name": "9.3", "images": {"hda_disk_image": "Rocky-9-GenericCloud-Base-9.3-20231113.0.x86_64.qcow2", "cdrom_image": "rocky-cloud-init-data.iso"}}, {"name": "9.2", "images": {"hda_disk_image": "Rocky-9-GenericCloud-Base-9.2-20230513.0.x86_64.qcow2", "cdrom_image": "rocky-cloud-init-data.iso"}}, {"name": "8.9", "images": {"hda_disk_image": "Rocky-8-GenericCloud-Base-8.9-20231119.0.x86_64.qcow2", "cdrom_image": "rocky-cloud-init-data.iso"}}, {"name": "8.8", "images": {"hda_disk_image": "Rocky-8-GenericCloud-Base-8.8-20230518.0.x86_64.qcow2", "cdrom_image": "rocky-cloud-init-data.iso"}}]}