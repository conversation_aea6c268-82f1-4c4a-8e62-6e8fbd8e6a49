/v2/projects/{project_id}/notifications/ws
------------------------------------------------------------------------------------------------------------------------------------------

.. contents::

GET /v2/projects/**{project_id}**/notifications/ws
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Receive notifications about projects from a Websocket

Parameters
**********
- **project_id**: Project UUID

Response status codes
**********************
- **200**: End of stream
- **404**: The project doesn't exist

