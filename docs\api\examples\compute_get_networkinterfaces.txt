curl -i -X GET 'http://localhost:3080/v2/compute/network/interfaces'

GET /v2/compute/network/interfaces HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 1601
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:16 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/network/interfaces

[
    {
        "id": "docker0",
        "ip_address": "**********",
        "mac_address": "02:42:0f:c7:42:69",
        "name": "docker0",
        "netmask": "***********",
        "special": true,
        "type": "ethernet"
    },
    {
        "id": "lo",
        "ip_address": "127.0.0.1",
        "mac_address": "00:00:00:00:00:00",
        "name": "lo",
        "netmask": "*********",
        "special": true,
        "type": "ethernet"
    },
    {
        "id": "virbr0",
        "ip_address": "*************",
        "mac_address": "52:54:00:d4:71:01",
        "name": "virbr0",
        "netmask": "*************",
        "special": true,
        "type": "ethernet"
    },
    {
        "id": "virbr0-nic",
        "ip_address": "",
        "mac_address": "52:54:00:d4:71:01",
        "name": "virbr0-nic",
        "netmask": "",
        "special": true,
        "type": "ethernet"
    },
    {
        "id": "vmnet1",
        "ip_address": "**********",
        "mac_address": "00:50:56:c0:00:01",
        "name": "vmnet1",
        "netmask": "*************",
        "special": true,
        "type": "ethernet"
    },
    {
        "id": "vmnet8",
        "ip_address": "*************",
        "mac_address": "00:50:56:c0:00:08",
        "name": "vmnet8",
        "netmask": "*************",
        "special": true,
        "type": "ethernet"
    },
    {
        "id": "wlp2s0",
        "ip_address": "*************",
        "mac_address": "9c:b6:d0:99:3c:07",
        "name": "wlp2s0",
        "netmask": "*************",
        "special": false,
        "type": "ethernet"
    }
]
