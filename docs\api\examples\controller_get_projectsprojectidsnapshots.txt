curl -i -X GET 'http://localhost:3080/v2/projects/39be72f3-b049-49f9-8cb4-d0c7c381f8a9/snapshots'

GET /v2/projects/39be72f3-b049-49f9-8cb4-d0c7c381f8a9/snapshots HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 197
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:38 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/snapshots

[
    {
        "created_at": 1578450515,
        "name": "test",
        "project_id": "39be72f3-b049-49f9-8cb4-d0c7c381f8a9",
        "snapshot_id": "9739f956-fb70-4424-bb44-a101f52f4e59"
    }
]
