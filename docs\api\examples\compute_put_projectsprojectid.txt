curl -i -X PUT 'http://localhost:3080/v2/compute/projects/51010203-0405-0607-0809-0a0b0c0d0e0f' -d '{"variables": [{"name": "TEST1", "value": "VAL1"}]}'

PUT /v2/compute/projects/51010203-0405-0607-0809-0a0b0c0d0e0f HTTP/1.1
{
    "variables": [
        {
            "name": "TEST1",
            "value": "VAL1"
        }
    ]
}


HTTP/1.1 200
Connection: close
Content-Length: 183
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:17 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}

{
    "name": "test",
    "project_id": "51010203-0405-0607-0809-0a0b0c0d0e0f",
    "variables": [
        {
            "name": "TEST1",
            "value": "VAL1"
        }
    ]
}
