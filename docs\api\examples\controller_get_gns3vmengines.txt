curl -i -X GET 'http://localhost:3080/v2/gns3vm/engines'

GET /v2/gns3vm/engines HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 1134
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:57 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/gns3vm/engines

[
    {
        "description": "VMware is the recommended choice for best performances.<br>The GNS3 VM can be <a href=\"https://github.com/GNS3/gns3-gui/releases/download/v2.2.4dev1/GNS3.VM.VMware.Workstation.2.2.4dev1.zip\">downloaded here</a>.",
        "engine_id": "vmware",
        "name": "VMware Workstation / Player (recommended)",
        "support_headless": true,
        "support_ram": true,
        "support_when_exit": true
    },
    {
        "description": "VirtualBox doesn't support nested virtualization, this means Qemu based VMs will run extremely slowly.<br>The GNS3 VM can be <a href=\"https://github.com/GNS3/gns3-gui/releases/download/v2.2.4dev1/GNS3.VM.VirtualBox.2.2.4dev1.zip\">downloaded here</a>",
        "engine_id": "virtualbox",
        "name": "VirtualBox",
        "support_headless": true,
        "support_ram": true,
        "support_when_exit": true
    },
    {
        "description": "Use a remote GNS3 server as the GNS3 VM.",
        "engine_id": "remote",
        "name": "Remote",
        "support_headless": false,
        "support_ram": false,
        "support_when_exit": false
    }
]
