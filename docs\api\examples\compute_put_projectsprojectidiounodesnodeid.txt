curl -i -X PUT 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/iou/nodes/3472f579-af40-40e7-8dde-e01b07cf1480' -d '{"console": 5005, "ethernet_adapters": 4, "l1_keepalives": true, "name": "test", "nvram": 2048, "ram": 512, "serial_adapters": 0, "use_default_iou_values": true}'

PUT /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/iou/nodes/3472f579-af40-40e7-8dde-e01b07cf1480 HTTP/1.1
{
    "console": 5005,
    "ethernet_adapters": 4,
    "l1_keepalives": true,
    "name": "test",
    "nvram": 2048,
    "ram": 512,
    "serial_adapters": 0,
    "use_default_iou_values": true
}


HTTP/1.1 200
Connection: close
Content-Length: 626
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:25:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/iou/nodes/{node_id}

{
    "application_id": 1,
    "command_line": "",
    "console": 5005,
    "console_type": "telnet",
    "ethernet_adapters": 4,
    "l1_keepalives": true,
    "md5sum": "e573e8f5c93c6c00783f20c7a170aa6c",
    "name": "test",
    "node_directory": "/tmp/pytest-of-grossmj/pytest-41/test_json4/project-files/iou/3472f579-af40-40e7-8dde-e01b07cf1480",
    "node_id": "3472f579-af40-40e7-8dde-e01b07cf1480",
    "nvram": 2048,
    "path": "iou.bin",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "ram": 512,
    "serial_adapters": 0,
    "status": "stopped",
    "usage": "",
    "use_default_iou_values": true
}
