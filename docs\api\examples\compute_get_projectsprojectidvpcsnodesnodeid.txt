curl -i -X GET 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vpcs/nodes/aa44ff6f-52fa-4bf9-9b93-2e73c9eb8071'

GET /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vpcs/nodes/aa44ff6f-52fa-4bf9-9b93-2e73c9eb8071 HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 384
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:47 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/vpcs/nodes/{node_id}

{
    "command_line": "",
    "console": 5004,
    "console_type": "telnet",
    "name": "PC TEST 1",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/vpcs/aa44ff6f-52fa-4bf9-9b93-2e73c9eb8071",
    "node_id": "aa44ff6f-52fa-4bf9-9b93-2e73c9eb8071",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "stopped"
}
