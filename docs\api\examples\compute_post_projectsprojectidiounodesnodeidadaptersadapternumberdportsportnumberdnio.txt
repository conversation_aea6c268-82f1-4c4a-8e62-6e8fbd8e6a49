curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/iou/nodes/fadca255-3eb8-44fa-9a14-31c52604d094/adapters/1/ports/0/nio' -d '{"ethernet_device": "docker0", "type": "nio_ethernet"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/iou/nodes/fadca255-3eb8-44fa-9a14-31c52604d094/adapters/1/ports/0/nio HTTP/1.1
{
    "ethernet_device": "docker0",
    "type": "nio_ethernet"
}


HTTP/1.1 201
Connection: close
Content-Length: 64
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:25:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/iou/nodes/{node_id}/adapters/{adapter_number:\d+}/ports/{port_number:\d+}/nio

{
    "ethernet_device": "docker0",
    "type": "nio_ethernet"
}
