{"appliance_id": "05512118-2b99-4789-90d3-5135665c3ec5", "name": "MikroTik CHR", "category": "router", "description": "Cloud Hosted Router (CHR) is a RouterOS version meant for running as a virtual machine. It supports x86 64-bit architecture and can be used on most of popular hypervisors such as VMWare, Hyper-V, VirtualBox, KVM and others. CHR has full RouterOS features enabled by default but has a different licensing model than other RouterOS versions.", "vendor_name": "MikroTik", "vendor_url": "http://mikrotik.com/", "documentation_url": "http://wiki.mikrotik.com/wiki/Manual:CHR", "product_name": "MikroTik Cloud Hosted Router", "product_url": "http://www.mikrotik.com/download", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "If you'd like a different sized main disk, resize the image before booting the VM for the first time.\n\nOn first boot, RouterOS is actually being installed, formatting the whole main virtual disk, before finally rebooting. That whole process may take a minute or so.\n\nThe console will become available after the installation is complete. Most Telnet/SSH clients (certainly SuperPutty) will keep retrying to connect, thus letting you know when installation is done.\n\nFrom that point on, everything about RouterOS is also true about Cloud Hosted Router, including the default credentials: Username \"admin\" and an empty password.\n\nThe primary differences between RouterOS and CHR are in support for virtual devices (this appliance comes with them being selected), and in the different license model, for which you can read more about at http://wiki.mikrotik.com/wiki/Manual:CHR.", "symbol": ":/symbols/router_firewall.svg", "port_name_format": "ether{port1}", "qemu": {"adapter_type": "virtio-net-pci", "adapters": 8, "ram": 384, "hda_disk_interface": "virtio", "arch": "x86_64", "console_type": "telnet", "boot_priority": "c", "kvm": "allow", "options": "-nographic"}, "images": [{"filename": "chr-7.16.img", "version": "7.16", "md5sum": "a4c2d00a87e73b3129cd66a4e0743c9a", "filesize": 134217728, "download_url": "http://www.mikrotik.com/download", "direct_download_url": "https://download.mikrotik.com/routeros/7.16/chr-7.16.img.zip", "compression": "zip"}, {"filename": "chr-7.15.3.img", "version": "7.15.3", "md5sum": "5af8c748a0de4e8e8b303180738721a9", "filesize": 134217728, "download_url": "http://www.mikrotik.com/download", "direct_download_url": "https://download.mikrotik.com/routeros/7.15.3/chr-7.15.3.img.zip", "compression": "zip"}, {"filename": "chr-7.14.3.img", "version": "7.14.3", "md5sum": "73f527efef81b529b267a0683cb87617", "filesize": 134217728, "download_url": "http://www.mikrotik.com/download", "direct_download_url": "https://download.mikrotik.com/routeros/7.14.3/chr-7.14.3.img.zip", "compression": "zip"}, {"filename": "chr-6.49.17.img", "version": "6.49.17", "md5sum": "ad9f4bd8cd4965a403350deeb5d35b96", "filesize": 67108864, "download_url": "http://www.mikrotik.com/download", "direct_download_url": "https://download.mikrotik.com/routeros/6.49.17/chr-6.49.17.img.zip", "compression": "zip"}, {"filename": "chr-6.49.13.img", "version": "6.49.13", "md5sum": "18349e1c3209495e571bcbee8a7e3259", "filesize": 67108864, "download_url": "http://www.mikrotik.com/download", "direct_download_url": "https://download.mikrotik.com/routeros/6.49.13/chr-6.49.13.img.zip", "compression": "zip"}], "versions": [{"name": "7.16", "images": {"hda_disk_image": "chr-7.16.img"}}, {"name": "7.15.3", "images": {"hda_disk_image": "chr-7.15.3.img"}}, {"name": "7.14.3", "images": {"hda_disk_image": "chr-7.14.3.img"}}, {"name": "6.49.17", "images": {"hda_disk_image": "chr-6.49.17.img"}}, {"name": "6.49.13", "images": {"hda_disk_image": "chr-6.49.13.img"}}]}