curl -i -X POST 'http://localhost:3080/v2/templates' -d '{"compute_id": "local", "hda_disk_image": "IOSvL2-********.55E.qcow2", "name": "Qemu template", "platform": "i386", "ram": 512, "template_type": "qemu"}'

POST /v2/templates HTTP/1.1
{
    "compute_id": "local",
    "hda_disk_image": "IOSvL2-********.55E.qcow2",
    "name": "Qemu template",
    "platform": "i386",
    "ram": 512,
    "template_type": "qemu"
}


HTTP/1.1 201
Connection: close
Content-Length: 1194
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:29:21 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/templates

{
    "adapter_type": "e1000",
    "adapters": 1,
    "bios_image": "",
    "boot_priority": "c",
    "builtin": false,
    "category": "guest",
    "cdrom_image": "",
    "compute_id": "local",
    "console_auto_start": false,
    "console_type": "telnet",
    "cpu_throttling": 0,
    "cpus": 1,
    "custom_adapters": [],
    "default_name_format": "{name}-{0}",
    "first_port_name": "",
    "hda_disk_image": "IOSvL2-********.55E.qcow2",
    "hda_disk_interface": "ide",
    "hdb_disk_image": "",
    "hdb_disk_interface": "ide",
    "hdc_disk_image": "",
    "hdc_disk_interface": "ide",
    "hdd_disk_image": "",
    "hdd_disk_interface": "ide",
    "initrd": "",
    "kernel_command_line": "",
    "kernel_image": "",
    "legacy_networking": false,
    "linked_clone": true,
    "mac_address": "",
    "name": "Qemu template",
    "on_close": "power_off",
    "options": "",
    "platform": "i386",
    "port_name_format": "Ethernet{0}",
    "port_segment_size": 0,
    "process_priority": "normal",
    "qemu_path": "",
    "ram": 512,
    "symbol": ":/symbols/qemu_guest.svg",
    "template_id": "f5ca540b-c7ac-4e58-8c78-1e2cd0b336db",
    "template_type": "qemu",
    "usage": ""
}
