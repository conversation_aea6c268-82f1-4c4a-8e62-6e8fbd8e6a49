curl -i -X POST 'http://localhost:3080/v2/compute/qemu/img' -d '{"cluster_size": 64, "format": "qcow2", "lazy_refcounts": "off", "path": "/tmp/hda.qcow2", "preallocation": "metadata", "qemu_img": "/tmp/qemu-img", "refcount_bits": 12, "size": 100}'

POST /v2/compute/qemu/img HTTP/1.1
{
    "cluster_size": 64,
    "format": "qcow2",
    "lazy_refcounts": "off",
    "path": "/tmp/hda.qcow2",
    "preallocation": "metadata",
    "qemu_img": "/tmp/qemu-img",
    "refcount_bits": 12,
    "size": 100
}


HTTP/1.1 201
Connection: close
Content-Length: 0
Content-Type: application/octet-stream
Date: Wed, 08 Jan 2020 02:27:19 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/qemu/img

