curl -i -X PUT 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/cloud/nodes/36d25ee9-96ff-4aca-ad57-b530f9618b21/adapters/0/ports/0/nio' -d '{"filters": {}, "lport": 4242, "rhost": "127.0.0.1", "rport": 4343, "type": "nio_udp"}'

PUT /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/cloud/nodes/36d25ee9-96ff-4aca-ad57-b530f9618b21/adapters/0/ports/0/nio HTTP/1.1
{
    "filters": {},
    "lport": 4242,
    "rhost": "127.0.0.1",
    "rport": 4343,
    "type": "nio_udp"
}


HTTP/1.1 201
Connection: close
Content-Length: 108
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:25:43 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/cloud/nodes/{node_id}/adapters/{adapter_number:\d+}/ports/{port_number:\d+}/nio

{
    "filters": {},
    "lport": 4242,
    "rhost": "127.0.0.1",
    "rport": 4343,
    "type": "nio_udp"
}
