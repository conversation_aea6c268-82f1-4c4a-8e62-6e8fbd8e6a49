curl -i -X GET 'http://localhost:3080/v2/computes/my_compute/qemu/images'

GET /v2/computes/my_compute/qemu/images HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 95
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:55 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/computes/{compute_id}/{emulator}/images

[
    {
        "filename": "linux.qcow2"
    },
    {
        "filename": "asav.qcow2"
    }
]
