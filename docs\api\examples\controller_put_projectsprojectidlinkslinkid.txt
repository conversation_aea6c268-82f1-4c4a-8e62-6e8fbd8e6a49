curl -i -X PUT 'http://localhost:3080/v2/projects/85333131-b83a-4112-9a51-184ba0c536a8/links/b76bd8b1-2171-4361-9228-801713d23079' -d '{"filters": {"frequency_drop": [50], "latency": [10]}, "nodes": [{"adapter_number": 0, "label": {"text": "Hello", "x": 64, "y": 0}, "node_id": "8b77b480-361e-488b-96b1-a769890e11ec", "port_number": 3}, {"adapter_number": 2, "node_id": "b4688a3d-0af8-4ddc-b85f-e26dc1031c4c", "port_number": 4}]}'

PUT /v2/projects/85333131-b83a-4112-9a51-184ba0c536a8/links/b76bd8b1-2171-4361-9228-801713d23079 HTTP/1.1
{
    "filters": {
        "frequency_drop": [
            50
        ],
        "latency": [
            10
        ]
    },
    "nodes": [
        {
            "adapter_number": 0,
            "label": {
                "text": "Hello",
                "x": 64,
                "y": 0
            },
            "node_id": "8b77b480-361e-488b-96b1-a769890e11ec",
            "port_number": 3
        },
        {
            "adapter_number": 2,
            "node_id": "b4688a3d-0af8-4ddc-b85f-e26dc1031c4c",
            "port_number": 4
        }
    ]
}


HTTP/1.1 201
Connection: close
Content-Length: 1027
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/links/{link_id}

{
    "capture_compute_id": null,
    "capture_file_name": null,
    "capture_file_path": null,
    "capturing": false,
    "filters": {
        "frequency_drop": [
            50
        ],
        "latency": [
            10
        ]
    },
    "link_id": "b76bd8b1-2171-4361-9228-801713d23079",
    "link_type": "ethernet",
    "nodes": [
        {
            "adapter_number": 0,
            "label": {
                "text": "Hello",
                "x": 64,
                "y": 0
            },
            "node_id": "8b77b480-361e-488b-96b1-a769890e11ec",
            "port_number": 3
        },
        {
            "adapter_number": 2,
            "label": {
                "style": "font-family: TypeWriter;font-size: 10.0;font-weight: bold;fill: #000000;fill-opacity: 1.0;",
                "text": "2/4"
            },
            "node_id": "b4688a3d-0af8-4ddc-b85f-e26dc1031c4c",
            "port_number": 4
        }
    ],
    "project_id": "85333131-b83a-4112-9a51-184ba0c536a8",
    "suspend": false
}
