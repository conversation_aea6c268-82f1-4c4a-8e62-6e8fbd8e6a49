curl -i -X PUT 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/docker/nodes/************************************' -d '{"console": 5006, "environment": "GNS3=1\nGNS4=0", "extra_hosts": "test:127.0.0.1", "name": "test", "start_command": "yes"}'

PUT /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/docker/nodes/************************************ HTTP/1.1
{
    "console": 5006,
    "environment": "GNS3=1\nGNS4=0",
    "extra_hosts": "test:127.0.0.1",
    "name": "test",
    "start_command": "yes"
}


HTTP/1.1 200
Connection: close
Content-Length: 681
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:25:51 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/docker/nodes/{node_id}

{
    "adapters": 2,
    "aux": 5005,
    "console": 5006,
    "console_http_path": "/",
    "console_http_port": 80,
    "console_resolution": "1280x1024",
    "console_type": "telnet",
    "container_id": "8bd8153ea8f5",
    "environment": "GNS3=1\nGNS4=0",
    "extra_hosts": "test:127.0.0.1",
    "extra_volumes": [],
    "image": "nginx:latest",
    "name": "test",
    "node_directory": "/tmp/pytest-of-grossmj/pytest-41/test_json4/project-files/docker/************************************",
    "node_id": "************************************",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "start_command": "yes",
    "status": "stopped",
    "usage": ""
}
