curl -i -X GET 'http://localhost:3080/v2/projects/75386d2c-4c0b-4190-ae35-d2db4807f560/nodes/3e1037e1-343f-44ea-8b7f-a653eb2d94cf'

GET /v2/projects/75386d2c-4c0b-4190-ae35-d2db4807f560/nodes/3e1037e1-343f-44ea-8b7f-a653eb2d94cf HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 1201
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:59 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/nodes/{node_id}

{
    "command_line": null,
    "compute_id": "example.com",
    "console": 2048,
    "console_auto_start": false,
    "console_host": "<MagicMock name='mock.console_host' id='140147704107360'>",
    "console_type": null,
    "custom_adapters": [],
    "first_port_name": null,
    "height": 59,
    "label": {
        "rotation": 0,
        "style": null,
        "text": "test",
        "x": null,
        "y": -40
    },
    "locked": false,
    "name": "test",
    "node_directory": null,
    "node_id": "3e1037e1-343f-44ea-8b7f-a653eb2d94cf",
    "node_type": "vpcs",
    "port_name_format": "Ethernet{0}",
    "port_segment_size": 0,
    "ports": [
        {
            "adapter_number": 0,
            "data_link_types": {
                "Ethernet": "DLT_EN10MB"
            },
            "link_type": "ethernet",
            "name": "Ethernet0",
            "port_number": 0,
            "short_name": "e0"
        }
    ],
    "project_id": "75386d2c-4c0b-4190-ae35-d2db4807f560",
    "properties": {
        "startup_script": "echo test"
    },
    "status": "stopped",
    "symbol": ":/symbols/computer.svg",
    "template_id": null,
    "width": 65,
    "x": 0,
    "y": 0,
    "z": 1
}
