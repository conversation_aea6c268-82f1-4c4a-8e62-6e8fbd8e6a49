curl -i -X GET 'http://localhost:3080/v2/templates/a1b6bb7d-f49a-4b68-8899-9a91c7abcc75'

GET /v2/templates/a1b6bb7d-f49a-4b68-8899-9a91c7abcc75 HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 378
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:29:20 GMT
Etag: "1d2e3b0f3945c728e2673eb94587c85d"
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/templates/{template_id}

{
    "base_script_file": "vpcs_base_config.txt",
    "builtin": false,
    "category": "guest",
    "compute_id": "local",
    "console_auto_start": false,
    "console_type": "telnet",
    "default_name_format": "PC{0}",
    "name": "VPCS_TEST",
    "symbol": ":/symbols/vpcs_guest.svg",
    "template_id": "a1b6bb7d-f49a-4b68-8899-9a91c7abcc75",
    "template_type": "vpcs"
}
