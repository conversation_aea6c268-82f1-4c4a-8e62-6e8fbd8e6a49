curl -i -X PUT 'http://localhost:3080/v2/templates/7b448d0f-a756-421d-9c97-8a339e5ec6c2' -d '{"base_script_file": "vpcs_base_config.txt", "category": "guest", "compute_id": "local", "console_auto_start": false, "console_type": "telnet", "default_name_format": "PC{0}", "name": "VPCS_TEST_RENAMED", "symbol": ":/symbols/vpcs_guest.svg", "template_id": "7b448d0f-a756-421d-9c97-8a339e5ec6c2", "template_type": "vpcs"}'

PUT /v2/templates/7b448d0f-a756-421d-9c97-8a339e5ec6c2 HTTP/1.1
{
    "base_script_file": "vpcs_base_config.txt",
    "category": "guest",
    "compute_id": "local",
    "console_auto_start": false,
    "console_type": "telnet",
    "default_name_format": "PC{0}",
    "name": "VPCS_TEST_RENAMED",
    "symbol": ":/symbols/vpcs_guest.svg",
    "template_id": "7b448d0f-a756-421d-9c97-8a339e5ec6c2",
    "template_type": "vpcs"
}


HTTP/1.1 200
Connection: close
Content-Length: 386
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:29:20 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/templates/{template_id}

{
    "base_script_file": "vpcs_base_config.txt",
    "builtin": false,
    "category": "guest",
    "compute_id": "local",
    "console_auto_start": false,
    "console_type": "telnet",
    "default_name_format": "PC{0}",
    "name": "VPCS_TEST_RENAMED",
    "symbol": ":/symbols/vpcs_guest.svg",
    "template_id": "7b448d0f-a756-421d-9c97-8a339e5ec6c2",
    "template_type": "vpcs"
}
