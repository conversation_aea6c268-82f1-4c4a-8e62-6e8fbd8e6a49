curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/docker/nodes/************************************/duplicate' -d '{"destination_node_id": "72462999-933a-4be7-9ef5-0528f04c3870"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/docker/nodes/************************************/duplicate HTTP/1.1
{
    "destination_node_id": "72462999-933a-4be7-9ef5-0528f04c3870"
}


HTTP/1.1 201
Connection: close
Content-Length: 4
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:25:51 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/docker/nodes/{node_id}/duplicate

true
