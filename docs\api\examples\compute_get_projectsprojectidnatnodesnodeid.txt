curl -i -X GET 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/nat/nodes/f76e8c5c-0a0c-451d-a4ba-f01286d06a16'

GET /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/nat/nodes/f76e8c5c-0a0c-451d-a4ba-f01286d06a16 HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 335
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:09 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/nat/nodes/{node_id}

{
    "name": "Nat 1",
    "node_id": "f76e8c5c-0a0c-451d-a4ba-f01286d06a16",
    "ports_mapping": [
        {
            "interface": "virbr0",
            "name": "nat0",
            "port_number": 0,
            "type": "ethernet"
        }
    ],
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "started"
}
