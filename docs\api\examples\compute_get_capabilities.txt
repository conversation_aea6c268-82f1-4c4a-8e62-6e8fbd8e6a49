curl -i -X GET 'http://localhost:3080/v2/compute/capabilities'

GET /v2/compute/capabilities HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 366
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:25:43 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/capabilities

{
    "node_types": [
        "cloud",
        "ethernet_hub",
        "ethernet_switch",
        "nat",
        "vpcs",
        "virtualbox",
        "dynamips",
        "frame_relay_switch",
        "atm_switch",
        "qemu",
        "vmware",
        "traceng",
        "docker",
        "iou"
    ],
    "platform": "linuxdebian",
    "version": "2.2.4dev1"
}
