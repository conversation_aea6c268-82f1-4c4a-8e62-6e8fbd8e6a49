curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/traceng/nodes/fe70324f-78a3-4ada-a675-bd693250d403/duplicate' -d '{"destination_node_id": "64e0db26-82e6-416a-ac11-97add4614131"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/traceng/nodes/fe70324f-78a3-4ada-a675-bd693250d403/duplicate HTTP/1.1
{
    "destination_node_id": "64e0db26-82e6-416a-ac11-97add4614131"
}


HTTP/1.1 201
Connection: close
Content-Length: 4
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:27 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/traceng/nodes/{node_id}/duplicate

true
