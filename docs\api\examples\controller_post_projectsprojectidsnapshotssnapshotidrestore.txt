curl -i -X POST 'http://localhost:3080/v2/projects/9aded720-678c-4fb8-849a-6f4275931e5b/snapshots/e1aa52cf-0003-4208-81a4-f45307793100/restore' -d '{}'

POST /v2/projects/9aded720-678c-4fb8-849a-6f4275931e5b/snapshots/e1aa52cf-0003-4208-81a4-f45307793100/restore HTTP/1.1
{}


HTTP/1.1 201
Connection: close
Content-Length: 560
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:55 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/snapshots/{snapshot_id}/restore

{
    "auto_close": true,
    "auto_open": false,
    "auto_start": false,
    "drawing_grid_size": 25,
    "filename": "test.gns3",
    "grid_size": 75,
    "name": "test",
    "path": "/tmp/tmptqqvzawm/projects/9aded720-678c-4fb8-849a-6f4275931e5b",
    "project_id": "9aded720-678c-4fb8-849a-6f4275931e5b",
    "scene_height": 1000,
    "scene_width": 2000,
    "show_grid": false,
    "show_interface_labels": false,
    "show_layers": false,
    "snap_to_grid": false,
    "status": "opened",
    "supplier": null,
    "variables": null,
    "zoom": 100
}
