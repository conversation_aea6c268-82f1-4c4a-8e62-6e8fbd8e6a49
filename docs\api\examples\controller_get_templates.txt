curl -i -X GET 'http://localhost:3080/v2/templates'

GET /v2/templates HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 3640
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:29:19 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/templates

[
    {
        "builtin": true,
        "category": "guest",
        "compute_id": null,
        "default_name_format": "Cloud{0}",
        "name": "Cloud",
        "symbol": ":/symbols/cloud.svg",
        "template_id": "39e257dc-8412-3174-b6b3-0ee3ed6a43e9",
        "template_type": "cloud"
    },
    {
        "builtin": true,
        "category": "guest",
        "compute_id": null,
        "default_name_format": "NAT{0}",
        "name": "NAT",
        "symbol": ":/symbols/cloud.svg",
        "template_id": "df8f4ea9-33b7-3e96-86a2-c39bc9bb649c",
        "template_type": "nat"
    },
    {
        "builtin": true,
        "category": "guest",
        "compute_id": null,
        "default_name_format": "PC{0}",
        "name": "VPCS",
        "properties": {
            "base_script_file": "vpcs_base_config.txt"
        },
        "symbol": ":/symbols/vpcs_guest.svg",
        "template_id": "19021f99-e36f-394d-b4a1-8aaa902ab9cc",
        "template_type": "vpcs"
    },
    {
        "builtin": true,
        "category": "switch",
        "compute_id": null,
        "console_type": "none",
        "default_name_format": "Switch{0}",
        "name": "Ethernet switch",
        "symbol": ":/symbols/ethernet_switch.svg",
        "template_id": "1966b864-93e7-32d5-965f-001384eec461",
        "template_type": "ethernet_switch"
    },
    {
        "builtin": true,
        "category": "switch",
        "compute_id": null,
        "default_name_format": "Hub{0}",
        "name": "Ethernet hub",
        "symbol": ":/symbols/hub.svg",
        "template_id": "b4503ea9-d6b6-3695-9fe4-1db3b39290b0",
        "template_type": "ethernet_hub"
    },
    {
        "builtin": true,
        "category": "switch",
        "compute_id": null,
        "default_name_format": "FRSW{0}",
        "name": "Frame Relay switch",
        "symbol": ":/symbols/frame_relay_switch.svg",
        "template_id": "dd0f6f3a-ba58-3249-81cb-a1dd88407a47",
        "template_type": "frame_relay_switch"
    },
    {
        "builtin": true,
        "category": "switch",
        "compute_id": null,
        "default_name_format": "ATMSW{0}",
        "name": "ATM switch",
        "symbol": ":/symbols/atm_switch.svg",
        "template_id": "aaa764e2-b383-300f-8a0e-3493bbfdb7d2",
        "template_type": "atm_switch"
    },
    {
        "adapter_type": "e1000",
        "adapters": 1,
        "bios_image": "",
        "boot_priority": "c",
        "builtin": false,
        "category": "router",
        "cdrom_image": "",
        "compute_id": "local",
        "console_auto_start": false,
        "console_type": "telnet",
        "cpu_throttling": 0,
        "cpus": 1,
        "custom_adapters": [],
        "default_name_format": "{name}-{0}",
        "first_port_name": "",
        "hda_disk_image": "",
        "hda_disk_interface": "ide",
        "hdb_disk_image": "",
        "hdb_disk_interface": "ide",
        "hdc_disk_image": "",
        "hdc_disk_interface": "ide",
        "hdd_disk_image": "",
        "hdd_disk_interface": "ide",
        "initrd": "",
        "kernel_command_line": "",
        "kernel_image": "",
        "legacy_networking": false,
        "linked_clone": true,
        "mac_address": "",
        "name": "test",
        "on_close": "power_off",
        "options": "",
        "platform": "i386",
        "port_name_format": "Ethernet{0}",
        "port_segment_size": 0,
        "process_priority": "normal",
        "qemu_path": "",
        "ram": 256,
        "symbol": "guest.svg",
        "template_id": "23ed031d-8265-4cba-8426-a8f58c794a10",
        "template_type": "qemu",
        "usage": ""
    }
]
