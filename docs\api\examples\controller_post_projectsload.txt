curl -i -X POST 'http://localhost:3080/v2/projects/load' -d '{"path": "/tmp/test.gns3"}'

POST /v2/projects/load HTTP/1.1
{
    "path": "/tmp/test.gns3"
}


HTTP/1.1 201
Connection: close
Content-Length: 560
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:01 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/load

{
    "auto_close": true,
    "auto_open": false,
    "auto_start": false,
    "drawing_grid_size": 25,
    "filename": "test.gns3",
    "grid_size": 75,
    "name": "test",
    "path": "/tmp/tmpe7ms410n/projects/f5007bef-f6d0-4950-a476-de9717ebd9c0",
    "project_id": "f5007bef-f6d0-4950-a476-de9717ebd9c0",
    "scene_height": 1000,
    "scene_width": 2000,
    "show_grid": false,
    "show_interface_labels": false,
    "show_layers": false,
    "snap_to_grid": false,
    "status": "opened",
    "supplier": null,
    "variables": null,
    "zoom": 100
}
