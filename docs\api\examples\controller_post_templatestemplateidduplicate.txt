curl -i -X POST 'http://localhost:3080/v2/templates/10ce7c5e-2f63-47ec-b52f-ac22e3be2748/duplicate' -d '{}'

POST /v2/templates/10ce7c5e-2f63-47ec-b52f-ac22e3be2748/duplicate HTTP/1.1
{}


HTTP/1.1 201
Connection: close
Content-Length: 378
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:29:20 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/templates/{template_id}/duplicate

{
    "base_script_file": "vpcs_base_config.txt",
    "builtin": false,
    "category": "guest",
    "compute_id": "local",
    "console_auto_start": false,
    "console_type": "telnet",
    "default_name_format": "PC{0}",
    "name": "VPCS_TEST",
    "symbol": ":/symbols/vpcs_guest.svg",
    "template_id": "a3857d20-292d-419b-be3c-a60d3040d8ce",
    "template_type": "vpcs"
}
