curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/qemu/nodes' -d '{"hda_disk_image": "hello.img", "name": "PC TEST 1", "qemu_path": "/tmp/tmphb4tqqk2/qemu-system-x86_64"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/qemu/nodes HTTP/1.1
{
    "hda_disk_image": "hello.img",
    "name": "PC TEST 1",
    "qemu_path": "/tmp/tmphb4tqqk2/qemu-system-x86_64"
}


HTTP/1.1 201
Connection: close
Content-Length: 1420
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:26:28 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/qemu/nodes

{
    "adapter_type": "e1000",
    "adapters": 1,
    "bios_image": "",
    "bios_image_md5sum": null,
    "boot_priority": "c",
    "cdrom_image": "",
    "cdrom_image_md5sum": null,
    "command_line": "",
    "console": 5004,
    "console_type": "telnet",
    "cpu_throttling": 0,
    "cpus": 1,
    "hda_disk_image": "hello.img",
    "hda_disk_image_md5sum": "7d793037a0760186574b0282f2f435e7",
    "hda_disk_interface": "ide",
    "hdb_disk_image": "",
    "hdb_disk_image_md5sum": null,
    "hdb_disk_interface": "ide",
    "hdc_disk_image": "",
    "hdc_disk_image_md5sum": null,
    "hdc_disk_interface": "ide",
    "hdd_disk_image": "",
    "hdd_disk_image_md5sum": null,
    "hdd_disk_interface": "ide",
    "initrd": "",
    "initrd_md5sum": null,
    "kernel_command_line": "",
    "kernel_image": "",
    "kernel_image_md5sum": null,
    "legacy_networking": false,
    "mac_address": "0c:dd:80:dd:e2:00",
    "name": "PC TEST 1",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/qemu/2320a0b9-ed9b-45d9-9bef-c6d13bd9dde2",
    "node_id": "2320a0b9-ed9b-45d9-9bef-c6d13bd9dde2",
    "on_close": "power_off",
    "options": "",
    "platform": "x86_64",
    "process_priority": "low",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "qemu_path": "/tmp/tmphb4tqqk2/qemu-system-x86_64",
    "ram": 256,
    "status": "stopped",
    "usage": ""
}
