description "GNS3 server"
author      "GNS3 Team"
env LANG=en_US.UTF-8

start on filesystem or runlevel [2345]
stop on shutdown

script
    exec start-stop-daemon --start -c gns3 --exec /usr/local/bin/gns3server -- --log /var/log/gns3.log --pid /var/run/gns3.pid --daemon
end script

pre-start script
    echo "[`date`] GNS3 Starting" >> /var/log/gns3.log
end script

pre-stop script
    echo "[`date`] GNS3 Stopping" >> /var/log/gns3.log
end script
