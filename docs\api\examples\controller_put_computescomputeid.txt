curl -i -X PUT 'http://localhost:3080/v2/computes/my_compute_id' -d '{"compute_id": "my_compute_id", "host": "localhost", "password": "secure", "port": 84, "protocol": "https", "user": "julien"}'

PUT /v2/computes/my_compute_id HTTP/1.1
{
    "compute_id": "my_compute_id",
    "host": "localhost",
    "password": "secure",
    "port": 84,
    "protocol": "https",
    "user": "julien"
}


HTTP/1.1 200
Connection: close
Content-Length: 359
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:51 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/computes/{compute_id}

{
    "capabilities": {
        "node_types": [],
        "version": null
    },
    "compute_id": "my_compute_id",
    "connected": false,
    "cpu_usage_percent": null,
    "host": "localhost",
    "last_error": null,
    "memory_usage_percent": null,
    "name": "http://julien@localhost:84",
    "port": 84,
    "protocol": "https",
    "user": "julien"
}
