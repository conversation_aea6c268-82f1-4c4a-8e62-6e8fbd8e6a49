curl -i -X PUT 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vmware/nodes/2178f4e5-4cac-4300-becf-1853fe7d2226' -d '{"console": 5005, "name": "test"}'

PUT /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vmware/nodes/2178f4e5-4cac-4300-becf-1853fe7d2226 HTTP/1.1
{
    "console": 5005,
    "name": "test"
}


HTTP/1.1 200
Connection: close
Content-Length: 612
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:43 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/vmware/nodes/{node_id}

{
    "adapter_type": "e1000",
    "adapters": 0,
    "console": 5005,
    "console_type": "telnet",
    "headless": false,
    "linked_clone": false,
    "name": "test",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/vmware/2178f4e5-4cac-4300-becf-1853fe7d2226",
    "node_id": "2178f4e5-4cac-4300-becf-1853fe7d2226",
    "on_close": "power_off",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "stopped",
    "usage": "",
    "use_any_adapter": false,
    "vmx_path": "/tmp/pytest-of-grossmj/pytest-41/test_vmware_update0/test.vmx"
}
