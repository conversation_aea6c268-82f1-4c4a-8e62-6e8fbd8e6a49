curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/iou/nodes/03423f2a-311d-4436-8c9a-8b34945f6cd2/start' -d '{"iourc_content": "test"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/iou/nodes/03423f2a-311d-4436-8c9a-8b34945f6cd2/start HTTP/1.1
{
    "iourc_content": "test"
}


HTTP/1.1 200
Connection: close
Content-Length: 631
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:25:57 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/iou/nodes/{node_id}/start

{
    "application_id": 1,
    "command_line": "",
    "console": 5004,
    "console_type": "telnet",
    "ethernet_adapters": 2,
    "l1_keepalives": false,
    "md5sum": "e573e8f5c93c6c00783f20c7a170aa6c",
    "name": "PC TEST 1",
    "node_directory": "/tmp/pytest-of-grossmj/pytest-41/test_json4/project-files/iou/03423f2a-311d-4436-8c9a-8b34945f6cd2",
    "node_id": "03423f2a-311d-4436-8c9a-8b34945f6cd2",
    "nvram": 128,
    "path": "iou.bin",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "ram": 256,
    "serial_adapters": 2,
    "status": "stopped",
    "usage": "",
    "use_default_iou_values": true
}
