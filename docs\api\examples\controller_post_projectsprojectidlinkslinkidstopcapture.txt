curl -i -X POST 'http://localhost:3080/v2/projects/9e00a8c0-272a-4f23-8f5a-2eacfb8d1c24/links/655c1eb3-1d29-4556-bb96-2007a15dea0f/stop_capture' -d '{}'

POST /v2/projects/9e00a8c0-272a-4f23-8f5a-2eacfb8d1c24/links/655c1eb3-1d29-4556-bb96-2007a15dea0f/stop_capture HTTP/1.1
{}


HTTP/1.1 201
Connection: close
Content-Length: 320
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/links/{link_id}/stop_capture

{
    "capture_compute_id": null,
    "capture_file_name": null,
    "capture_file_path": null,
    "capturing": false,
    "filters": {},
    "link_id": "655c1eb3-1d29-4556-bb96-2007a15dea0f",
    "link_type": "ethernet",
    "nodes": [],
    "project_id": "9e00a8c0-272a-4f23-8f5a-2eacfb8d1c24",
    "suspend": false
}
