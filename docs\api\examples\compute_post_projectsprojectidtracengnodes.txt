curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/traceng/nodes' -d '{"name": "TraceNG TEST 1"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/traceng/nodes HTTP/1.1
{
    "name": "TraceNG TEST 1"
}


HTTP/1.1 201
Connection: close
Content-Length: 443
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:26 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/traceng/nodes

{
    "command_line": "",
    "console": null,
    "console_type": "none",
    "default_destination": "",
    "ip_address": "",
    "name": "TraceNG TEST 1",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/traceng/f27f5247-e419-4404-9150-b1bb9306d783",
    "node_id": "f27f5247-e419-4404-9150-b1bb9306d783",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "stopped"
}
