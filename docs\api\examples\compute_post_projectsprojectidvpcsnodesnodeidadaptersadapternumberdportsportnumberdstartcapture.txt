curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vpcs/nodes/a6e2aa26-3bc9-48ce-83dd-c2d7bc32dcb2/adapters/0/ports/0/start_capture' -d '{"capture_file_name": "test.pcap", "data_link_type": "DLT_EN10MB"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/vpcs/nodes/a6e2aa26-3bc9-48ce-83dd-c2d7bc32dcb2/adapters/0/ports/0/start_capture HTTP/1.1
{
    "capture_file_name": "test.pcap",
    "data_link_type": "DLT_EN10MB"
}


HTTP/1.1 200
Connection: close
Content-Length: 123
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:48 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/vpcs/nodes/{node_id}/adapters/{adapter_number:\d+}/ports/{port_number:\d+}/start_capture

{
    "pcap_file_path": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/captures/test.pcap"
}
