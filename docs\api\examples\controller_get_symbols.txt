curl -i -X GET 'http://localhost:3080/v2/symbols'

GET /v2/symbols HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 60126
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:29:18 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/symbols

[
    {
        "builtin": true,
        "filename": "PBX.svg",
        "symbol_id": ":/symbols/classic/PBX.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "PIX_firewall.svg",
        "symbol_id": ":/symbols/classic/PIX_firewall.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "access_point.svg",
        "symbol_id": ":/symbols/classic/access_point.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "access_server.svg",
        "symbol_id": ":/symbols/classic/access_server.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "asa.svg",
        "symbol_id": ":/symbols/classic/asa.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "atm.svg",
        "symbol_id": ":/symbols/affinity/square/blue/atm.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "atm.svg",
        "symbol_id": ":/symbols/affinity/square/red/atm.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "atm.svg",
        "symbol_id": ":/symbols/affinity/square/gray/atm.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "atm.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/atm.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "atm.svg",
        "symbol_id": ":/symbols/affinity/circle/red/atm.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "atm.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/atm.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "atm_bridge.svg",
        "symbol_id": ":/symbols/classic/atm_bridge.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "atm_switch.svg",
        "symbol_id": ":/symbols/classic/atm_switch.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "call_manager.svg",
        "symbol_id": ":/symbols/classic/call_manager.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "camera.svg",
        "symbol_id": ":/symbols/affinity/square/blue/camera.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "camera.svg",
        "symbol_id": ":/symbols/affinity/square/red/camera.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "camera.svg",
        "symbol_id": ":/symbols/affinity/square/gray/camera.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "camera.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/camera.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "camera.svg",
        "symbol_id": ":/symbols/affinity/circle/red/camera.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "camera.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/camera.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "camera_dome.svg",
        "symbol_id": ":/symbols/affinity/square/blue/camera_dome.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "camera_dome.svg",
        "symbol_id": ":/symbols/affinity/square/red/camera_dome.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "camera_dome.svg",
        "symbol_id": ":/symbols/affinity/square/gray/camera_dome.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "camera_dome.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/camera_dome.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "camera_dome.svg",
        "symbol_id": ":/symbols/affinity/circle/red/camera_dome.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "camera_dome.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/camera_dome.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "circle.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/circle.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "circle.svg",
        "symbol_id": ":/symbols/affinity/circle/red/circle.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "circle.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/circle.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "client.svg",
        "symbol_id": ":/symbols/affinity/square/blue/client.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "client.svg",
        "symbol_id": ":/symbols/affinity/square/red/client.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "client.svg",
        "symbol_id": ":/symbols/affinity/square/gray/client.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "client.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/client.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "client.svg",
        "symbol_id": ":/symbols/affinity/circle/red/client.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "client.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/client.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "client_vm.svg",
        "symbol_id": ":/symbols/affinity/square/blue/client_vm.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "client_vm.svg",
        "symbol_id": ":/symbols/affinity/square/red/client_vm.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "client_vm.svg",
        "symbol_id": ":/symbols/affinity/square/gray/client_vm.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "client_vm.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/client_vm.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "client_vm.svg",
        "symbol_id": ":/symbols/affinity/circle/red/client_vm.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "client_vm.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/client_vm.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "cloud.svg",
        "symbol_id": ":/symbols/affinity/square/blue/cloud.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "cloud.svg",
        "symbol_id": ":/symbols/affinity/square/red/cloud.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "cloud.svg",
        "symbol_id": ":/symbols/affinity/square/gray/cloud.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "cloud.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/cloud.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "cloud.svg",
        "symbol_id": ":/symbols/affinity/circle/red/cloud.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "cloud.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/cloud.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "cloud.svg",
        "symbol_id": ":/symbols/classic/cloud.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "cog.svg",
        "symbol_id": ":/symbols/affinity/square/blue/cog.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "cog.svg",
        "symbol_id": ":/symbols/affinity/square/red/cog.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "cog.svg",
        "symbol_id": ":/symbols/affinity/square/gray/cog.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "cog.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/cog.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "cog.svg",
        "symbol_id": ":/symbols/affinity/circle/red/cog.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "cog.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/cog.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "communications.svg",
        "symbol_id": ":/symbols/affinity/square/blue/communications.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "communications.svg",
        "symbol_id": ":/symbols/affinity/square/red/communications.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "communications.svg",
        "symbol_id": ":/symbols/affinity/square/gray/communications.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "communications.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/communications.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "communications.svg",
        "symbol_id": ":/symbols/affinity/circle/red/communications.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "communications.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/communications.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "computer.svg",
        "symbol_id": ":/symbols/classic/computer.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "conversation.svg",
        "symbol_id": ":/symbols/affinity/square/blue/conversation.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "conversation.svg",
        "symbol_id": ":/symbols/affinity/square/red/conversation.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "conversation.svg",
        "symbol_id": ":/symbols/affinity/square/gray/conversation.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "conversation.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/conversation.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "conversation.svg",
        "symbol_id": ":/symbols/affinity/circle/red/conversation.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "conversation.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/conversation.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "docker.svg",
        "symbol_id": ":/symbols/affinity/square/blue/docker.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "docker.svg",
        "symbol_id": ":/symbols/affinity/square/red/docker.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "docker.svg",
        "symbol_id": ":/symbols/affinity/square/gray/docker.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "docker.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/docker.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "docker.svg",
        "symbol_id": ":/symbols/affinity/circle/red/docker.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "docker.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/docker.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "docker_guest.svg",
        "symbol_id": ":/symbols/classic/docker_guest.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "dslam.svg",
        "symbol_id": ":/symbols/affinity/square/blue/dslam.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "dslam.svg",
        "symbol_id": ":/symbols/affinity/square/red/dslam.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "dslam.svg",
        "symbol_id": ":/symbols/affinity/square/gray/dslam.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "dslam.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/dslam.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "dslam.svg",
        "symbol_id": ":/symbols/affinity/circle/red/dslam.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "dslam.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/dslam.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "dslam.svg",
        "symbol_id": ":/symbols/classic/dslam.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "edge_label_switch_router.svg",
        "symbol_id": ":/symbols/classic/edge_label_switch_router.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "ethernet_switch.svg",
        "symbol_id": ":/symbols/classic/ethernet_switch.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "fingerprint.svg",
        "symbol_id": ":/symbols/affinity/square/blue/fingerprint.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "fingerprint.svg",
        "symbol_id": ":/symbols/affinity/square/red/fingerprint.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "fingerprint.svg",
        "symbol_id": ":/symbols/affinity/square/gray/fingerprint.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "fingerprint.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/fingerprint.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "fingerprint.svg",
        "symbol_id": ":/symbols/affinity/circle/red/fingerprint.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "fingerprint.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/fingerprint.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "firewall.svg",
        "symbol_id": ":/symbols/affinity/square/blue/firewall.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "firewall.svg",
        "symbol_id": ":/symbols/affinity/square/red/firewall.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "firewall.svg",
        "symbol_id": ":/symbols/affinity/square/gray/firewall.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "firewall.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/firewall.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "firewall.svg",
        "symbol_id": ":/symbols/affinity/circle/red/firewall.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "firewall.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/firewall.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "firewall.svg",
        "symbol_id": ":/symbols/classic/firewall.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "firewall3.svg",
        "symbol_id": ":/symbols/affinity/square/blue/firewall3.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "firewall3.svg",
        "symbol_id": ":/symbols/affinity/square/red/firewall3.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "firewall3.svg",
        "symbol_id": ":/symbols/affinity/square/gray/firewall3.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "firewall3.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/firewall3.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "firewall3.svg",
        "symbol_id": ":/symbols/affinity/circle/red/firewall3.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "firewall3.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/firewall3.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "frame_relay_switch.svg",
        "symbol_id": ":/symbols/classic/frame_relay_switch.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "gateway.svg",
        "symbol_id": ":/symbols/classic/gateway.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "grid.svg",
        "symbol_id": ":/symbols/affinity/square/blue/grid.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "grid.svg",
        "symbol_id": ":/symbols/affinity/square/red/grid.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "grid.svg",
        "symbol_id": ":/symbols/affinity/square/gray/grid.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "grid.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/grid.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "grid.svg",
        "symbol_id": ":/symbols/affinity/circle/red/grid.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "grid.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/grid.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "grid2.svg",
        "symbol_id": ":/symbols/affinity/square/blue/grid2.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "grid2.svg",
        "symbol_id": ":/symbols/affinity/square/red/grid2.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "grid2.svg",
        "symbol_id": ":/symbols/affinity/square/gray/grid2.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "grid2.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/grid2.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "grid2.svg",
        "symbol_id": ":/symbols/affinity/circle/red/grid2.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "grid2.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/grid2.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "hub.svg",
        "symbol_id": ":/symbols/affinity/square/blue/hub.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "hub.svg",
        "symbol_id": ":/symbols/affinity/square/red/hub.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "hub.svg",
        "symbol_id": ":/symbols/affinity/square/gray/hub.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "hub.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/hub.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "hub.svg",
        "symbol_id": ":/symbols/affinity/circle/red/hub.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "hub.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/hub.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "hub.svg",
        "symbol_id": ":/symbols/classic/hub.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "ids.svg",
        "symbol_id": ":/symbols/classic/ids.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "inspect.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/inspect.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "inspect.svg",
        "symbol_id": ":/symbols/affinity/circle/red/inspect.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "inspect.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/inspect.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "inspect2.svg",
        "symbol_id": ":/symbols/affinity/square/blue/inspect2.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "inspect2.svg",
        "symbol_id": ":/symbols/affinity/square/red/inspect2.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "inspect2.svg",
        "symbol_id": ":/symbols/affinity/square/gray/inspect2.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "inspect3.svg",
        "symbol_id": ":/symbols/affinity/square/blue/inspect3.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "inspect3.svg",
        "symbol_id": ":/symbols/affinity/square/red/inspect3.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "inspect3.svg",
        "symbol_id": ":/symbols/affinity/square/gray/inspect3.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "inspect3.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/inspect3.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "inspect3.svg",
        "symbol_id": ":/symbols/affinity/circle/red/inspect3.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "inspect3.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/inspect3.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "interconnect.svg",
        "symbol_id": ":/symbols/affinity/square/blue/interconnect.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "interconnect.svg",
        "symbol_id": ":/symbols/affinity/square/red/interconnect.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "interconnect.svg",
        "symbol_id": ":/symbols/affinity/square/gray/interconnect.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "interconnect.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/interconnect.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "interconnect.svg",
        "symbol_id": ":/symbols/affinity/circle/red/interconnect.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "interconnect.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/interconnect.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "iosv_l2_virl.svg",
        "symbol_id": ":/symbols/classic/iosv_l2_virl.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "iosv_virl.svg",
        "symbol_id": ":/symbols/classic/iosv_virl.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "ip_phone.svg",
        "symbol_id": ":/symbols/classic/ip_phone.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "isdn.svg",
        "symbol_id": ":/symbols/affinity/square/blue/isdn.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "isdn.svg",
        "symbol_id": ":/symbols/affinity/square/red/isdn.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "isdn.svg",
        "symbol_id": ":/symbols/affinity/square/gray/isdn.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "isdn.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/isdn.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "isdn.svg",
        "symbol_id": ":/symbols/affinity/circle/red/isdn.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "isdn.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/isdn.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "label_switch_router.svg",
        "symbol_id": ":/symbols/classic/label_switch_router.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "ldap.svg",
        "symbol_id": ":/symbols/affinity/square/blue/ldap.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "ldap.svg",
        "symbol_id": ":/symbols/affinity/square/red/ldap.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "ldap.svg",
        "symbol_id": ":/symbols/affinity/square/gray/ldap.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "ldap.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/ldap.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "ldap.svg",
        "symbol_id": ":/symbols/affinity/circle/red/ldap.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "ldap.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/ldap.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "light_bulb.svg",
        "symbol_id": ":/symbols/affinity/square/blue/light_bulb.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "light_bulb.svg",
        "symbol_id": ":/symbols/affinity/square/red/light_bulb.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "light_bulb.svg",
        "symbol_id": ":/symbols/affinity/square/gray/light_bulb.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "light_bulb.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/light_bulb.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "light_bulb.svg",
        "symbol_id": ":/symbols/affinity/circle/red/light_bulb.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "light_bulb.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/light_bulb.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "lightweight_ap.svg",
        "symbol_id": ":/symbols/classic/lightweight_ap.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "link.svg",
        "symbol_id": ":/symbols/affinity/square/blue/link.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "link.svg",
        "symbol_id": ":/symbols/affinity/square/red/link.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "link.svg",
        "symbol_id": ":/symbols/affinity/square/gray/link.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "link.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/link.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "link.svg",
        "symbol_id": ":/symbols/affinity/circle/red/link.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "link.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/link.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "loadbalancer.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/loadbalancer.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "loadbalancer.svg",
        "symbol_id": ":/symbols/affinity/circle/red/loadbalancer.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "loadbalancer.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/loadbalancer.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "multilayer_switch.svg",
        "symbol_id": ":/symbols/classic/multilayer_switch.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "nas.svg",
        "symbol_id": ":/symbols/affinity/square/blue/nas.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "nas.svg",
        "symbol_id": ":/symbols/affinity/square/red/nas.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "nas.svg",
        "symbol_id": ":/symbols/affinity/square/gray/nas.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "nas.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/nas.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "nas.svg",
        "symbol_id": ":/symbols/affinity/circle/red/nas.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "nas.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/nas.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "optical_router.svg",
        "symbol_id": ":/symbols/classic/optical_router.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "pinpoint.svg",
        "symbol_id": ":/symbols/affinity/square/blue/pinpoint.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "pinpoint.svg",
        "symbol_id": ":/symbols/affinity/square/red/pinpoint.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "pinpoint.svg",
        "symbol_id": ":/symbols/affinity/square/gray/pinpoint.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "pinpoint.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/pinpoint.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "pinpoint.svg",
        "symbol_id": ":/symbols/affinity/circle/red/pinpoint.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "pinpoint.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/pinpoint.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "printer.svg",
        "symbol_id": ":/symbols/affinity/square/blue/printer.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "printer.svg",
        "symbol_id": ":/symbols/affinity/square/red/printer.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "printer.svg",
        "symbol_id": ":/symbols/affinity/square/gray/printer.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "printer.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/printer.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "printer.svg",
        "symbol_id": ":/symbols/affinity/circle/red/printer.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "printer.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/printer.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "printer.svg",
        "symbol_id": ":/symbols/classic/printer.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "qemu_guest.svg",
        "symbol_id": ":/symbols/classic/qemu_guest.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "rj45.svg",
        "symbol_id": ":/symbols/affinity/square/blue/rj45.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "rj45.svg",
        "symbol_id": ":/symbols/affinity/square/red/rj45.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "rj45.svg",
        "symbol_id": ":/symbols/affinity/square/gray/rj45.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "rj45.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/rj45.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "rj45.svg",
        "symbol_id": ":/symbols/affinity/circle/red/rj45.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "rj45.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/rj45.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "route_switch_processor.svg",
        "symbol_id": ":/symbols/classic/route_switch_processor.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "router.awp.svg",
        "symbol_id": ":/symbols/classic/router.awp.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "router.svg",
        "symbol_id": ":/symbols/affinity/square/blue/router.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "router.svg",
        "symbol_id": ":/symbols/affinity/square/red/router.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "router.svg",
        "symbol_id": ":/symbols/affinity/square/gray/router.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "router.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/router.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "router.svg",
        "symbol_id": ":/symbols/affinity/circle/red/router.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "router.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/router.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "router.svg",
        "symbol_id": ":/symbols/classic/router.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "router_cloud.svg",
        "symbol_id": ":/symbols/affinity/square/blue/router_cloud.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "router_cloud.svg",
        "symbol_id": ":/symbols/affinity/square/red/router_cloud.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "router_cloud.svg",
        "symbol_id": ":/symbols/affinity/square/gray/router_cloud.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "router_cloud.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/router_cloud.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "router_cloud.svg",
        "symbol_id": ":/symbols/affinity/circle/red/router_cloud.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "router_cloud.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/router_cloud.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "router_firewall.svg",
        "symbol_id": ":/symbols/classic/router_firewall.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "router_netflow.svg",
        "symbol_id": ":/symbols/classic/router_netflow.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "satellite.svg",
        "symbol_id": ":/symbols/affinity/square/blue/satellite.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "satellite.svg",
        "symbol_id": ":/symbols/affinity/square/red/satellite.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "satellite.svg",
        "symbol_id": ":/symbols/affinity/square/gray/satellite.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "satellite.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/satellite.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "satellite.svg",
        "symbol_id": ":/symbols/affinity/circle/red/satellite.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "satellite.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/satellite.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "satellite_dish.svg",
        "symbol_id": ":/symbols/affinity/square/blue/satellite_dish.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "satellite_dish.svg",
        "symbol_id": ":/symbols/affinity/square/red/satellite_dish.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "satellite_dish.svg",
        "symbol_id": ":/symbols/affinity/square/gray/satellite_dish.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "satellite_dish.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/satellite_dish.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "satellite_dish.svg",
        "symbol_id": ":/symbols/affinity/circle/red/satellite_dish.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "satellite_dish.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/satellite_dish.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "scull.svg",
        "symbol_id": ":/symbols/affinity/square/blue/scull.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "scull.svg",
        "symbol_id": ":/symbols/affinity/square/red/scull.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "scull.svg",
        "symbol_id": ":/symbols/affinity/square/gray/scull.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "scull.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/scull.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "scull.svg",
        "symbol_id": ":/symbols/affinity/circle/red/scull.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "scull.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/scull.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "server-cluster.svg",
        "symbol_id": ":/symbols/affinity/square/blue/server-cluster.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "server-cluster.svg",
        "symbol_id": ":/symbols/affinity/square/red/server-cluster.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "server-cluster.svg",
        "symbol_id": ":/symbols/affinity/square/gray/server-cluster.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "server-cluster.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/server-cluster.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "server-cluster.svg",
        "symbol_id": ":/symbols/affinity/circle/red/server-cluster.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "server-cluster.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/server-cluster.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "server.svg",
        "symbol_id": ":/symbols/affinity/square/blue/server.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "server.svg",
        "symbol_id": ":/symbols/affinity/square/red/server.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "server.svg",
        "symbol_id": ":/symbols/affinity/square/gray/server.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "server.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/server.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "server.svg",
        "symbol_id": ":/symbols/affinity/circle/red/server.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "server.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/server.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "server.svg",
        "symbol_id": ":/symbols/classic/server.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "sip_server.svg",
        "symbol_id": ":/symbols/classic/sip_server.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "square.svg",
        "symbol_id": ":/symbols/affinity/square/blue/square.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "square.svg",
        "symbol_id": ":/symbols/affinity/square/red/square.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "square.svg",
        "symbol_id": ":/symbols/affinity/square/gray/square.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "statistics.svg",
        "symbol_id": ":/symbols/affinity/square/blue/statistics.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "statistics.svg",
        "symbol_id": ":/symbols/affinity/square/red/statistics.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "statistics.svg",
        "symbol_id": ":/symbols/affinity/square/gray/statistics.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "statistics.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/statistics.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "statistics.svg",
        "symbol_id": ":/symbols/affinity/circle/red/statistics.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "statistics.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/statistics.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "storage.svg",
        "symbol_id": ":/symbols/affinity/square/blue/storage.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "storage.svg",
        "symbol_id": ":/symbols/affinity/square/red/storage.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "storage.svg",
        "symbol_id": ":/symbols/affinity/square/gray/storage.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "storage.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/storage.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "storage.svg",
        "symbol_id": ":/symbols/affinity/circle/red/storage.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "storage.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/storage.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "switch.svg",
        "symbol_id": ":/symbols/affinity/square/blue/switch.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "switch.svg",
        "symbol_id": ":/symbols/affinity/square/red/switch.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "switch.svg",
        "symbol_id": ":/symbols/affinity/square/gray/switch.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "switch.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/switch.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "switch.svg",
        "symbol_id": ":/symbols/affinity/circle/red/switch.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "switch.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/switch.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "switch_multilayer.svg",
        "symbol_id": ":/symbols/affinity/square/blue/switch_multilayer.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "switch_multilayer.svg",
        "symbol_id": ":/symbols/affinity/square/red/switch_multilayer.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "switch_multilayer.svg",
        "symbol_id": ":/symbols/affinity/square/gray/switch_multilayer.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "switch_multilayer.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/switch_multilayer.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "switch_multilayer.svg",
        "symbol_id": ":/symbols/affinity/circle/red/switch_multilayer.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "switch_multilayer.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/switch_multilayer.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "tablet.svg",
        "symbol_id": ":/symbols/affinity/square/blue/tablet.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "tablet.svg",
        "symbol_id": ":/symbols/affinity/square/red/tablet.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "tablet.svg",
        "symbol_id": ":/symbols/affinity/square/gray/tablet.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "tablet.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/tablet.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "tablet.svg",
        "symbol_id": ":/symbols/affinity/circle/red/tablet.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "tablet.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/tablet.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "traceng.svg",
        "symbol_id": ":/symbols/classic/traceng.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "tree.svg",
        "symbol_id": ":/symbols/affinity/square/blue/tree.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "tree.svg",
        "symbol_id": ":/symbols/affinity/square/red/tree.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "tree.svg",
        "symbol_id": ":/symbols/affinity/square/gray/tree.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "tree.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/tree.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "tree.svg",
        "symbol_id": ":/symbols/affinity/circle/red/tree.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "tree.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/tree.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "user.svg",
        "symbol_id": ":/symbols/affinity/square/blue/user.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "user.svg",
        "symbol_id": ":/symbols/affinity/square/red/user.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "user.svg",
        "symbol_id": ":/symbols/affinity/square/gray/user.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "user.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/user.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "user.svg",
        "symbol_id": ":/symbols/affinity/circle/red/user.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "user.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/user.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "vbox_guest.svg",
        "symbol_id": ":/symbols/classic/vbox_guest.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "virtualbox.svg",
        "symbol_id": ":/symbols/affinity/square/blue/virtualbox.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "virtualbox.svg",
        "symbol_id": ":/symbols/affinity/square/red/virtualbox.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "virtualbox.svg",
        "symbol_id": ":/symbols/affinity/square/gray/virtualbox.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "virtualbox.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/virtualbox.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "virtualbox.svg",
        "symbol_id": ":/symbols/affinity/circle/red/virtualbox.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "virtualbox.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/virtualbox.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "vm.svg",
        "symbol_id": ":/symbols/affinity/square/blue/vm.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "vm.svg",
        "symbol_id": ":/symbols/affinity/square/red/vm.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "vm.svg",
        "symbol_id": ":/symbols/affinity/square/gray/vm.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "vm.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/vm.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "vm.svg",
        "symbol_id": ":/symbols/affinity/circle/red/vm.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "vm.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/vm.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "vmware.svg",
        "symbol_id": ":/symbols/affinity/square/blue/vmware.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "vmware.svg",
        "symbol_id": ":/symbols/affinity/square/red/vmware.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "vmware.svg",
        "symbol_id": ":/symbols/affinity/square/gray/vmware.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "vmware.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/vmware.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "vmware.svg",
        "symbol_id": ":/symbols/affinity/circle/red/vmware.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "vmware.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/vmware.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "vmware_guest.svg",
        "symbol_id": ":/symbols/classic/vmware_guest.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "voice_access_server.svg",
        "symbol_id": ":/symbols/classic/voice_access_server.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "voice_router.svg",
        "symbol_id": ":/symbols/classic/voice_router.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "vpcs_guest.svg",
        "symbol_id": ":/symbols/classic/vpcs_guest.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "wifi.svg",
        "symbol_id": ":/symbols/affinity/square/blue/wifi.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "wifi.svg",
        "symbol_id": ":/symbols/affinity/square/red/wifi.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "wifi.svg",
        "symbol_id": ":/symbols/affinity/square/gray/wifi.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "wifi.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/wifi.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "wifi.svg",
        "symbol_id": ":/symbols/affinity/circle/red/wifi.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "wifi.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/wifi.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "wlan_controller.svg",
        "symbol_id": ":/symbols/classic/wlan_controller.svg",
        "theme": "Classic"
    },
    {
        "builtin": true,
        "filename": "wlc.svg",
        "symbol_id": ":/symbols/affinity/square/blue/wlc.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "wlc.svg",
        "symbol_id": ":/symbols/affinity/square/red/wlc.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "wlc.svg",
        "symbol_id": ":/symbols/affinity/square/gray/wlc.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "wlc.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/wlc.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "wlc.svg",
        "symbol_id": ":/symbols/affinity/circle/red/wlc.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "wlc.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/wlc.svg",
        "theme": "Affinity-circle-gray"
    },
    {
        "builtin": true,
        "filename": "xml.svg",
        "symbol_id": ":/symbols/affinity/square/blue/xml.svg",
        "theme": "Affinity-square-blue"
    },
    {
        "builtin": true,
        "filename": "xml.svg",
        "symbol_id": ":/symbols/affinity/square/red/xml.svg",
        "theme": "Affinity-square-red"
    },
    {
        "builtin": true,
        "filename": "xml.svg",
        "symbol_id": ":/symbols/affinity/square/gray/xml.svg",
        "theme": "Affinity-square-gray"
    },
    {
        "builtin": true,
        "filename": "xml.svg",
        "symbol_id": ":/symbols/affinity/circle/blue/xml.svg",
        "theme": "Affinity-circle-blue"
    },
    {
        "builtin": true,
        "filename": "xml.svg",
        "symbol_id": ":/symbols/affinity/circle/red/xml.svg",
        "theme": "Affinity-circle-red"
    },
    {
        "builtin": true,
        "filename": "xml.svg",
        "symbol_id": ":/symbols/affinity/circle/gray/xml.svg",
        "theme": "Affinity-circle-gray"
    }
]
