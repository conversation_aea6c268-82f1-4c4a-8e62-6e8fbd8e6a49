/v2/computes/{compute_id}/{emulator}/{action:.+}
------------------------------------------------------------------------------------------------------------------------------------------

.. contents::

GET /v2/computes/**{compute_id}**/**{emulator}**/**{action:.+}**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Forward call specific to compute node. Read the full compute API for available actions

Parameters
**********
- **compute_id**: Compute UUID

Response status codes
**********************
- **200**: OK
- **404**: Instance doesn't exist


POST /v2/computes/**{compute_id}**/**{emulator}**/**{action:.+}**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Forward call specific to compute node. Read the full compute API for available actions

Parameters
**********
- **compute_id**: Compute UUID

Response status codes
**********************
- **200**: OK
- **404**: Instance doesn't exist


PUT /v2/computes/**{compute_id}**/**{emulator}**/**{action:.+}**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Forward call specific to compute node. Read the full compute API for available actions

Parameters
**********
- **compute_id**: Compute UUID

Response status codes
**********************
- **200**: OK
- **404**: Instance doesn't exist

