curl -i -X POST 'http://localhost:3080/v2/projects/3c2c807b-0298-4fb6-9e21-163092ae2e3e/nodes/9feeb107-2f8c-4233-b195-4f2703be347d/duplicate' -d '{"x": 10, "y": 5, "z": 0}'

POST /v2/projects/3c2c807b-0298-4fb6-9e21-163092ae2e3e/nodes/9feeb107-2f8c-4233-b195-4f2703be347d/duplicate HTTP/1.1
{
    "x": 10,
    "y": 5,
    "z": 0
}


HTTP/1.1 201
Connection: close
Content-Length: 1161
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:28:00 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/nodes/{node_id}/duplicate

{
    "command_line": null,
    "compute_id": "example.com",
    "console": null,
    "console_auto_start": false,
    "console_host": "<MagicMock name='mock.console_host' id='140147692996200'>",
    "console_type": null,
    "custom_adapters": [],
    "first_port_name": null,
    "height": 59,
    "label": {
        "rotation": 0,
        "style": null,
        "text": "test1",
        "x": null,
        "y": -40
    },
    "locked": false,
    "name": "test1",
    "node_directory": null,
    "node_id": "eb676094-e625-4e9e-bb84-72af5bd0fb12",
    "node_type": "vpcs",
    "port_name_format": "Ethernet{0}",
    "port_segment_size": 0,
    "ports": [
        {
            "adapter_number": 0,
            "data_link_types": {
                "Ethernet": "DLT_EN10MB"
            },
            "link_type": "ethernet",
            "name": "Ethernet0",
            "port_number": 0,
            "short_name": "e0"
        }
    ],
    "project_id": "3c2c807b-0298-4fb6-9e21-163092ae2e3e",
    "properties": {},
    "status": "stopped",
    "symbol": ":/symbols/computer.svg",
    "template_id": null,
    "width": 65,
    "x": 10,
    "y": 5,
    "z": 0
}
