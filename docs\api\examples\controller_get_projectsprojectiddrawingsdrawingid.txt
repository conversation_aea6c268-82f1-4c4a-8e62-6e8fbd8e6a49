curl -i -X GET 'http://localhost:3080/v2/projects/a4ef9f61-07ca-4744-8e65-079ced0e4fdf/drawings/2ca0b412-917d-4ba3-85c5-c75c72565d39'

GET /v2/projects/a4ef9f61-07ca-4744-8e65-079ced0e4fdf/drawings/2ca0b412-917d-4ba3-85c5-c75c72565d39 HTTP/1.1



HTTP/1.1 200
Connection: close
Content-Length: 344
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:57 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/drawings/{drawing_id}

{
    "drawing_id": "2ca0b412-917d-4ba3-85c5-c75c72565d39",
    "locked": false,
    "project_id": "a4ef9f61-07ca-4744-8e65-079ced0e4fdf",
    "rotation": 0,
    "svg": "<svg height=\"210\" width=\"500\"><line x1=\"0\" y1=\"0\" x2=\"200\" y2=\"200\" style=\"stroke:rgb(255,0,0);stroke-width:2\" /></svg>",
    "x": 10,
    "y": 20,
    "z": 0
}
