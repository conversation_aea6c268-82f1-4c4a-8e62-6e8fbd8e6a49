curl -i -X POST 'http://localhost:3080/v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/traceng/nodes/9ba1f859-a8f5-46b7-beda-ce49116bdce7/start' -d '{"destination": "***********"}'

POST /v2/compute/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/traceng/nodes/9ba1f859-a8f5-46b7-beda-ce49116bdce7/start HTTP/1.1
{
    "destination": "***********"
}


HTTP/1.1 200
Connection: close
Content-Length: 443
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:26 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/compute/projects/{project_id}/traceng/nodes/{node_id}/start

{
    "command_line": "",
    "console": null,
    "console_type": "none",
    "default_destination": "",
    "ip_address": "",
    "name": "TraceNG TEST 1",
    "node_directory": "/tmp/tmp3gc2avyo/projects/a1e920ca-338a-4e9f-b363-aa607b09dd80/project-files/traceng/9ba1f859-a8f5-46b7-beda-ce49116bdce7",
    "node_id": "9ba1f859-a8f5-46b7-beda-ce49116bdce7",
    "project_id": "a1e920ca-338a-4e9f-b363-aa607b09dd80",
    "status": "stopped"
}
