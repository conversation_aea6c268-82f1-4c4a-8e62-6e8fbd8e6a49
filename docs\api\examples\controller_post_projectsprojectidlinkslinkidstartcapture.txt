curl -i -X POST 'http://localhost:3080/v2/projects/d90f8e95-fda3-49d5-86b5-4c41b2fac7f2/links/514ae6a3-8078-4bb9-9298-1faa599399b9/start_capture' -d '{}'

POST /v2/projects/d90f8e95-fda3-49d5-86b5-4c41b2fac7f2/links/514ae6a3-8078-4bb9-9298-1faa599399b9/start_capture HTTP/1.1
{}


HTTP/1.1 201
Connection: close
Content-Length: 320
Content-Type: application/json
Date: Wed, 08 Jan 2020 02:27:58 GMT
Server: Python/3.6 GNS3/2.2.4dev1
X-Route: /v2/projects/{project_id}/links/{link_id}/start_capture

{
    "capture_compute_id": null,
    "capture_file_name": null,
    "capture_file_path": null,
    "capturing": false,
    "filters": {},
    "link_id": "514ae6a3-8078-4bb9-9298-1faa599399b9",
    "link_type": "ethernet",
    "nodes": [],
    "project_id": "d90f8e95-fda3-49d5-86b5-4c41b2fac7f2",
    "suspend": false
}
