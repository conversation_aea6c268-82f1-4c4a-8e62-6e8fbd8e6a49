{"appliance_id": "a5d624de-9af2-4e7e-b8e1-055df9aaa446", "name": "<PERSON><PERSON>", "category": "multilayer_switch", "description": "The Open Network Operating System (ONOS) is a software defined networking (SDN) OS for service providers that has scalability, high availability, high performance and abstractions to make it easy to create apps and services. The platform is based on a solid architecture and has quickly matured to be feature rich and production ready. The community has grown to include over 50 partners and collaborators that contribute to all aspects of the project including interesting use cases such as CORD", "vendor_name": "<PERSON><PERSON>", "vendor_url": "http://onosproject.org/", "documentation_url": "https://wiki.onosproject.org", "product_name": "<PERSON><PERSON>", "product_url": "http://onosproject.org/", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "docker": {"adapters": 1, "image": "onosproject/onos:latest"}}