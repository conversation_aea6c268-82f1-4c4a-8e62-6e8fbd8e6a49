{"appliance_id": "5a03e6b9-afe3-44f5-ae35-5664c0250b94", "name": "CoreOS", "category": "guest", "description": "CoreOS is designed for security, consistency, and reliability. Instead of installing packages via yum or apt, CoreOS uses Linux containers to manage your services at a higher level of abstraction. A single service's code and all dependencies are packaged within a container that can be run on one or many CoreOS machines.", "vendor_name": "CoreOS, Inc", "vendor_url": "https://coreos.com/", "vendor_logo_url": "https://raw.githubusercontent.com/GNS3/gns3-registry/master/vendor-logos/CoreOS.png", "documentation_url": "https://coreos.com/docs/", "product_name": "CoreOS", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "qemu": {"adapter_type": "virtio-net-pci", "adapters": 1, "ram": 1024, "hda_disk_interface": "virtio", "hdd_disk_interface": "ide", "arch": "x86_64", "console_type": "telnet", "kvm": "allow"}, "images": [{"filename": "coreos_production_qemu_image.img", "version": "2079.4.0", "md5sum": "d5e28d68bcadf252ff9c909a159b9504", "filesize": 970129408, "download_url": "http://stable.release.core-os.net/amd64-usr/2079.4.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/2079.4.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.2023.5.0.img", "version": "2023.5.0", "md5sum": "f8b12e524aa81220222d06473b3f36dc", "filesize": 587661312, "download_url": "http://stable.release.core-os.net/amd64-usr/2023.5.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/2023.5.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1911.5.0.img", "version": "1911.5.0", "md5sum": "3b5b33697cfc545d8eb9fb461c612e76", "filesize": 940507136, "download_url": "http://stable.release.core-os.net/amd64-usr/1911.5.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1911.5.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1855.5.0.img", "version": "1855.5.0", "md5sum": "6b5b06bc47446277c5c536c09b5a7988", "filesize": 924319744, "download_url": "http://stable.release.core-os.net/amd64-usr/1855.5.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1855.5.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1688.5.3.img", "version": "1688.5.3", "md5sum": "f1015a9573fb6e378d2a5e86b4243acd", "filesize": 867368960, "download_url": "http://stable.release.core-os.net/amd64-usr/1688.5.3/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1688.5.3/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1632.2.1.img", "version": "1632.2.1", "md5sum": "facd05ca85eb87e2dc6aefd6779f6806", "filesize": 885719040, "download_url": "http://stable.release.core-os.net/amd64-usr/1632.2.1/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1632.2.1/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1576.4.0.img", "version": "1576.4.0", "md5sum": "7d3c647807afe1f18fd0c76730e612b4", "filesize": 849739776, "download_url": "http://stable.release.core-os.net/amd64-usr/1576.4.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1576.4.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1520.8.0.img", "version": "1520.8.0", "md5sum": "a69fb2cd3ae475f9afbc268f7d391e83", "filesize": 842661888, "download_url": "http://stable.release.core-os.net/amd64-usr/1520.8.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1520.8.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1465.7.0.img", "version": "1465.7.0", "md5sum": "1db77d47e76d3d9082846584e0f4b4bc", "filesize": 796590080, "download_url": "http://stable.release.core-os.net/amd64-usr/1465.7.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1465.7.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1409.7.0.img", "version": "1409.7.0", "md5sum": "b8db4a07bac71468ed47bd09bedc1bdf", "filesize": 812187648, "download_url": "http://stable.release.core-os.net/amd64-usr/1409.7.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1409.7.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1353.8.0.img", "version": "1353.8.0", "md5sum": "f84bf924d7b30190539a14e14d94d4f8", "filesize": 795934720, "download_url": "http://stable.release.core-os.net/amd64-usr/1353.8.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1353.8.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1353.7.0.img", "version": "1353.7.0", "md5sum": "2d4ecc377b41ee5b1ffd90090548ebc0", "filesize": 796852224, "download_url": "http://stable.release.core-os.net/amd64-usr/1353.7.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1353.7.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1235.9.0.img", "version": "1235.9.0", "md5sum": "77a256ceaa0da6960391c03ebfe5388c", "filesize": 795869184, "download_url": "http://stable.release.core-os.net/amd64-usr/1235.9.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1235.9.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1235.8.0.img", "version": "1235.8.0", "md5sum": "0eec78690fd9f6d3b9e8d8ff41bc10b5", "filesize": 785252352, "download_url": "http://stable.release.core-os.net/amd64-usr/1235.8.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1235.8.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1235.6.0.img", "version": "1235.6.0", "md5sum": "2ff81c223be4bfa40c9ef765bb0d7f26", "filesize": 784990208, "download_url": "http://stable.release.core-os.net/amd64-usr/1235.6.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1235.6.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1235.5.0.img", "version": "1235.5.0", "md5sum": "11aa05a27654b66a4e6dfb1e9f1c7ff9", "filesize": 792592384, "download_url": "http://stable.release.core-os.net/amd64-usr/1235.5.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1235.5.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1235.4.0.img", "version": "1235.4.0", "md5sum": "c59930b3b1ad0716c91a62ac56234d97", "filesize": 787415040, "download_url": "http://stable.release.core-os.net/amd64-usr/1235.4.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1235.4.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1185.5.0.img", "version": "1185.5.0", "md5sum": "97b6eaa9857c68c67e56d7b742d43f5e", "filesize": 754843648, "download_url": "http://stable.release.core-os.net/amd64-usr/1185.5.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1185.5.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.1185.3.0.img", "version": "1185.3.0", "md5sum": "a1b6b69e5a58a1900b145b024340eff0", "filesize": 753926144, "download_url": "http://stable.release.core-os.net/amd64-usr/1185.3.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/1185.3.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}, {"filename": "coreos_production_qemu_image.835.9.img", "version": "835.9.0", "md5sum": "768a5df35784a014ba06609da88f5158", "filesize": 635633664, "download_url": "http://stable.release.core-os.net/amd64-usr/835.9.0/", "direct_download_url": "http://stable.release.core-os.net/amd64-usr/835.9.0/coreos_production_qemu_image.img.bz2", "compression": "bzip2"}], "versions": [{"name": "2079.4.0", "images": {"hda_disk_image": "coreos_production_qemu_image.img"}}, {"name": "2023.5.0", "images": {"hda_disk_image": "coreos_production_qemu_image.2023.5.0.img"}}, {"name": "1911.5.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1911.5.0.img"}}, {"name": "1855.5.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1855.5.0.img"}}, {"name": "1688.5.3", "images": {"hda_disk_image": "coreos_production_qemu_image.1688.5.3.img"}}, {"name": "1632.2.1", "images": {"hda_disk_image": "coreos_production_qemu_image.1632.2.1.img"}}, {"name": "1576.4.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1576.4.0.img"}}, {"name": "1520.8.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1520.8.0.img"}}, {"name": "1465.7.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1465.7.0.img"}}, {"name": "1409.7.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1409.7.0.img"}}, {"name": "1353.8.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1353.8.0.img"}}, {"name": "1353.7.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1353.7.0.img"}}, {"name": "1235.9.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1235.9.0.img"}}, {"name": "1235.8.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1235.8.0.img"}}, {"name": "1235.6.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1235.6.0.img"}}, {"name": "1235.5.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1235.5.0.img"}}, {"name": "1235.4.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1235.4.0.img"}}, {"name": "1185.5.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1185.5.0.img"}}, {"name": "1185.3.0", "images": {"hda_disk_image": "coreos_production_qemu_image.1185.3.0.img"}}, {"name": "835.9.0", "images": {"hda_disk_image": "coreos_production_qemu_image.835.9.img"}}]}